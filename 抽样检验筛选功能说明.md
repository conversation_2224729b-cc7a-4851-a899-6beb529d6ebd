# 抽样检验记录筛选功能说明

## 功能概述
在抽样检验记录页面的标题下方新增了一个筛选区域，提供多维度的数据筛选功能，帮助用户快速找到所需的检验记录。

## 筛选字段

### 1. 报告编号
- **类型**：文本输入框
- **功能**：支持模糊搜索
- **使用方法**：输入报告编号的全部或部分内容
- **示例**：输入"ABC"可以匹配"ABC001"、"ABC002"等

### 2. 供应商
- **类型**：下拉选择框
- **功能**：精确匹配供应商
- **选项**：动态加载系统中已有的供应商列表
- **默认值**："全部供应商"

### 3. 检验类型
- **类型**：下拉选择框
- **功能**：精确匹配检验类型
- **选项**：
  - 全部类型
  - 来料检验
  - 首件检验
  - 巡检
  - 终检
  - 特殊检验

### 4. 不良率
- **类型**：数值输入框 + 比较操作符
- **功能**：支持多种数值比较
- **比较操作符**：
  - 大于 (>)
  - 大于等于 (>=)
  - 小于 (<)
  - 小于等于 (<=)
  - 等于 (=)
- **输入范围**：0-100（百分比）
- **精度**：支持小数点后两位

### 5. 检验结果
- **类型**：下拉选择框
- **功能**：精确匹配检验结果
- **选项**：
  - 全部结果
  - 合格
  - 不合格
  - 待检
  - 让步接收

## 操作按钮

### 筛选按钮
- **图标**：🔍 搜索图标
- **功能**：应用当前设置的筛选条件
- **快捷键**：在报告编号和不良率输入框中按回车键

### 重置按钮
- **图标**：↶ 重置图标
- **功能**：清空所有筛选条件并刷新页面
- **效果**：保留日期范围设置，清除所有筛选条件

## 筛选逻辑

### 组合方式
- 所有筛选条件采用**"与"（AND）**的关联形式
- 只有同时满足所有设置的筛选条件的记录才会显示
- 空白或未选择的筛选条件将被忽略

### 匹配规则
- **模糊匹配**：报告编号（支持部分匹配）
- **精确匹配**：供应商、检验类型、检验结果
- **数值比较**：不良率（根据选择的操作符进行比较）

## 使用示例

### 示例1：查找特定供应商的不合格记录
1. 供应商：选择"供应商A"
2. 检验结果：选择"不合格"
3. 点击"筛选"按钮

### 示例2：查找不良率大于5%的来料检验记录
1. 检验类型：选择"来料检验"
2. 不良率：选择"大于"，输入"5"
3. 点击"筛选"按钮

### 示例3：查找特定报告编号
1. 报告编号：输入"ABC001"
2. 点击"筛选"按钮或按回车键

## 技术特性

### 状态保持
- 筛选后的条件会保存在URL参数中
- 分页、刷新页面时筛选状态不会丢失
- 支持浏览器前进后退功能

### 响应式设计
- 在移动设备上自动调整布局
- 筛选字段在小屏幕上垂直排列
- 保持良好的用户体验

### 性能优化
- 筛选在服务器端执行，减少数据传输
- 支持分页，避免一次性加载大量数据
- 合理的索引设计提高查询效率

## 与现有功能的集成

### 日期筛选
- 新增的筛选功能与现有的日期筛选功能完全兼容
- 可以同时使用日期范围和其他筛选条件
- 重置操作不会影响日期设置

### 快速搜索
- 新增筛选功能与原有的快速搜索功能并存
- 可以同时使用快速搜索和详细筛选
- 提供更灵活的搜索方式

### 分页功能
- 筛选结果支持分页显示
- 分页链接自动包含筛选参数
- 每页显示数量设置得到保留

## 数据库查询优化

### 查询条件
```sql
-- 示例查询（包含所有筛选条件）
SELECT * FROM sampling_inspection 
WHERE inspection_date BETWEEN ? AND ?
  AND report_code LIKE '%ABC%'
  AND supplier = '供应商A'
  AND inspection_type = '来料检验'
  AND sample_quantity > 0 
  AND (defect_quantity / sample_quantity * 100) > 5.0
  AND inspection_result = '不合格'
ORDER BY inspection_date DESC
LIMIT 20 OFFSET 0;
```

### 索引建议
为了提高查询性能，建议在以下字段上创建索引：
- `inspection_date`（已有）
- `report_code`
- `supplier`
- `inspection_type`
- `inspection_result`
- 复合索引：`(supplier, inspection_type, inspection_result)`

## 用户界面设计

### 视觉风格
- 采用卡片式设计，与页面整体风格保持一致
- 使用浅灰色背景区分筛选区域
- 统一的字体大小和颜色规范

### 交互体验
- 清晰的标签和占位符文本
- 焦点状态的视觉反馈
- 按钮的悬停效果
- 合理的间距和对齐

### 可访问性
- 支持键盘导航
- 合理的Tab顺序
- 清晰的视觉层次
- 适当的颜色对比度

## 未来扩展

### 可能的增强功能
1. **保存筛选方案**：允许用户保存常用的筛选条件组合
2. **高级筛选**：支持更复杂的逻辑组合（OR、NOT等）
3. **筛选历史**：记录用户的筛选历史
4. **导出功能**：支持导出筛选结果
5. **实时筛选**：输入时实时显示结果

### 性能优化
1. **缓存机制**：缓存常用的筛选结果
2. **异步加载**：大数据量时的分批加载
3. **搜索建议**：提供智能搜索建议

## 总结

新增的筛选功能大大提升了抽样检验记录的查询效率和用户体验：

1. **功能完整**：覆盖了主要的筛选需求
2. **操作简便**：直观的界面设计，易于使用
3. **性能良好**：服务器端筛选，响应迅速
4. **扩展性强**：为未来功能扩展预留了空间
5. **兼容性好**：与现有功能完美集成

通过这些筛选功能，用户可以快速定位到所需的检验记录，提高工作效率。
