#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物料确认相关数据库表创建脚本
从检验系统迁移 material_sample_confirmation_form 和相关表结构
"""

import mysql.connector
import sys
import os

# 添加父目录到路径以导入 db_config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_config import get_db_connection

def create_material_confirmation_tables():
    """创建物料确认相关的数据库表"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始创建物料确认相关数据库表...")
        
        # 创建 Material_Sample_Confirmation_Form 基本信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_confirmation_form (
                id INT AUTO_INCREMENT PRIMARY KEY,
                report_code VARCHAR(20) NOT NULL UNIQUE,
                supplier TEXT,
                inspection_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sample_count INT,
                inspector TEXT,
                material_number TEXT,
                graph_number TEXT,
                material_name TEXT,
                drawing_version TEXT,
                material_texture TEXT,
                surface_processing TEXT,
                sample_status TEXT,
                other_textbox TEXT,
                final_judgment VARCHAR(10) DEFAULT '',
                opinion TEXT,
                review VARCHAR(10)
            )
        """)
        print("✅ material_sample_confirmation_form 基本信息表创建成功")

        # 创建尺寸数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_size_data (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                size_number INT,
                position TEXT,
                value DECIMAL(10, 2),
                min_value DECIMAL(10, 2),
                max_value DECIMAL(10, 2),
                measure_1 DECIMAL(10, 2),
                measure_2 DECIMAL(10, 2),
                measure_3 DECIMAL(10, 2),
                measure_4 DECIMAL(10, 2),
                measure_5 DECIMAL(10, 2),
                check_result TEXT,
                note TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id) ON DELETE CASCADE
            )
        """)
        print("✅ material_sample_size_data 尺寸数据表创建成功")

        # 创建外观检查表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_appearance (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                check_number INT,
                check_result TEXT,
                note TEXT,
                other_info TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id) ON DELETE CASCADE
            )
        """)
        print("✅ material_sample_appearance 外观检查表创建成功")

        # 创建功能检查表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_function (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                check_number INT,
                check_result TEXT,
                note TEXT,
                burnin_info TEXT,
                electrical_info TEXT,
                tests_info TEXT,
                other_test TEXT,
                other_info TEXT,
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id) ON DELETE CASCADE
            )
        """)
        print("✅ material_sample_function 功能检查表创建成功")

        # 创建问题记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_sample_questions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                form_id INT,
                question_number INT,
                question_text TEXT,
                image_path varchar(255),
                FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id) ON DELETE CASCADE
            )
        """)
        print("✅ material_sample_questions 问题记录表创建成功")

        # 提交事务
        conn.commit()
        print("\n🎉 所有物料确认相关表创建成功！")
        
        # 显示表信息
        cursor.execute("SHOW TABLES LIKE 'material_sample%'")
        tables = cursor.fetchall()
        print(f"\n📋 已创建的表 ({len(tables)} 个):")
        for table in tables:
            print(f"   - {table[0]}")
            
    except mysql.connector.Error as err:
        print(f"❌ 数据库错误: {err}")
        return False
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

def check_tables_exist():
    """检查表是否已存在"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        tables_to_check = [
            'material_sample_confirmation_form',
            'material_sample_size_data', 
            'material_sample_appearance',
            'material_sample_function',
            'material_sample_questions'
        ]
        
        existing_tables = []
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
        
        cursor.close()
        conn.close()
        
        return existing_tables
        
    except Exception as e:
        print(f"❌ 检查表存在性失败: {e}")
        return []

if __name__ == "__main__":
    print("🔍 检查物料确认表是否已存在...")
    existing_tables = check_tables_exist()
    
    if existing_tables:
        print(f"📋 发现已存在的表: {existing_tables}")
        response = input("是否要重新创建这些表? (y/N): ").lower().strip()
        if response != 'y':
            print("❌ 操作已取消")
            sys.exit(0)
    
    if create_material_confirmation_tables():
        print("\n✅ 物料确认数据库表迁移完成！")
    else:
        print("\n❌ 物料确认数据库表迁移失败！")
        sys.exit(1)
