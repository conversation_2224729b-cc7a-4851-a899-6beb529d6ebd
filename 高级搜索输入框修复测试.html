<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级搜索输入框修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h2 {
            color: #1976d2;
            margin-top: 0;
        }
        
        .test-button {
            background-color: #1976d2;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background-color: #1565c0;
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            pointer-events: auto;
        }
        
        .modal.show {
            pointer-events: auto;
        }
        
        .modal-content {
            background-color: #fefefe;
            margin: 8% auto;
            padding: 20px;
            border: none;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            position: relative;
            z-index: 10001;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        
        .close:hover,
        .close:focus {
            color: black;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            pointer-events: auto !important;
            cursor: text !important;
            z-index: 10002 !important;
            position: relative !important;
        }
        
        .form-control:focus {
            border-color: #1976d2;
            outline: none;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        
        .two-column-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .test-steps {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        .test-steps h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-steps ol {
            margin-bottom: 0;
        }
        
        .problem-description {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .problem-description h3 {
            color: #c62828;
            margin-top: 0;
        }
        
        .solution-description {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .solution-description h3 {
            color: #2e7d32;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>高级搜索输入框修复测试</h2>
            <p>此页面用于测试高级搜索模态框中输入框无法输入的问题是否已修复。</p>
        </div>
        
        <div class="problem-description">
            <h3>问题描述</h3>
            <ul>
                <li>高级搜索文本框无法输入</li>
                <li>似乎有元素遮挡输入框</li>
                <li>可能是z-index层级问题</li>
                <li>pointer-events设置不正确</li>
            </ul>
        </div>
        
        <div class="solution-description">
            <h3>修复方案</h3>
            <ul>
                <li>调整z-index层级，避免冲突</li>
                <li>确保模态框显示时设置正确的pointer-events</li>
                <li>为输入框添加强制的交互样式</li>
                <li>改进模态框的显示和隐藏逻辑</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="openModal()">打开高级搜索</button>
        
        <div class="test-steps">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击"打开高级搜索"按钮</li>
                <li>尝试在各个输入框中输入文字</li>
                <li>检查是否可以正常获得焦点</li>
                <li>检查是否可以正常输入和编辑</li>
                <li>测试选择框是否可以正常点击</li>
                <li>点击模态框外部或关闭按钮测试关闭功能</li>
            </ol>
        </div>
    </div>

    <!-- 高级搜索模态框 -->
    <div id="test-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 style="margin-top: 0;">高级搜索测试</h2>
            
            <div class="two-column-layout">
                <div>
                    <div class="form-group">
                        <label class="form-label">物料料号</label>
                        <input type="text" class="form-control" placeholder="请输入物料料号">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">物料名称</label>
                        <input type="text" class="form-control" placeholder="请输入物料名称">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">供应商</label>
                        <input type="text" class="form-control" placeholder="请输入供应商">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">采购订单</label>
                        <input type="text" class="form-control" placeholder="请输入采购订单">
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label class="form-label">检验结果</label>
                        <select class="form-control">
                            <option value="">全部</option>
                            <option value="合格">合格</option>
                            <option value="不合格">不合格</option>
                            <option value="待检">待检</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">检验开始日期</label>
                        <input type="date" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">检验结束日期</label>
                        <input type="date" class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">备注</label>
                        <textarea class="form-control" rows="3" placeholder="请输入备注"></textarea>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="test-button" type="button">搜索</button>
                <button class="test-button" type="button" onclick="closeModal()" style="background-color: #666; margin-left: 10px;">取消</button>
            </div>
        </div>
    </div>

    <script>
        function openModal() {
            const modal = document.getElementById('test-modal');
            modal.style.display = 'block';
            modal.classList.add('show');
            // 确保模态框可以交互
            modal.style.pointerEvents = 'auto';
            modal.style.zIndex = '10000';
            
            console.log('模态框已打开');
            
            // 自动聚焦到第一个输入框
            setTimeout(() => {
                const firstInput = modal.querySelector('input[type="text"]');
                if (firstInput) {
                    firstInput.focus();
                    console.log('已聚焦到第一个输入框');
                }
            }, 100);
        }

        function closeModal() {
            const modal = document.getElementById('test-modal');
            modal.style.display = 'none';
            modal.classList.remove('show');
            console.log('模态框已关闭');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('test-modal');
            if (event.target === modal) {
                closeModal();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        // 测试输入框交互
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    console.log('输入框获得焦点:', this.placeholder || this.tagName);
                });
                
                input.addEventListener('input', function() {
                    console.log('输入内容:', this.value);
                });
            });
        });
    </script>
</body>
</html>
