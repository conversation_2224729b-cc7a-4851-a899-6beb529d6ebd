#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理现有数据并重新生成示例数据
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_config import get_db_connection
import mysql.connector

def clean_existing_data():
    """清理现有的示例数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🧹 开始清理现有数据...")
        
        # 清理检验记录
        cursor.execute("DELETE FROM sampling_inspection")
        print("✅ 清理抽样检验记录")
        
        # 清理物料数据（保留用户可能手动添加的数据，只清理示例数据）
        cursor.execute("DELETE FROM materials WHERE material_number LIKE 'MAT%'")
        print("✅ 清理示例物料数据")
        
        conn.commit()
        print("✅ 数据清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据清理失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def regenerate_sample_data():
    """重新生成示例数据"""
    try:
        print("\n🔄 重新生成示例数据...")
        
        # 导入生成示例数据的模块
        from generate_sample_data import generate_materials_data, generate_inspection_data
        
        # 生成物料数据
        if generate_materials_data():
            print("✅ 物料数据生成成功")
        else:
            print("❌ 物料数据生成失败")
            return False
        
        # 生成检验数据
        if generate_inspection_data():
            print("✅ 检验数据生成成功")
        else:
            print("❌ 检验数据生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 示例数据生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始清理并重新生成示例数据")
    print("=" * 50)
    
    # 清理现有数据
    if not clean_existing_data():
        print("❌ 数据清理失败，程序退出")
        sys.exit(1)
    
    # 重新生成示例数据
    if not regenerate_sample_data():
        print("❌ 示例数据生成失败，程序退出")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 数据清理和重新生成完成！")

if __name__ == "__main__":
    main()
