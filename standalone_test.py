#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试load-data功能
"""

from flask import Flask
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

app = Flask(__name__)

@app.route('/')
def index():
    return '''
    <h1>Load-Data功能测试</h1>
    <p><a href="/test-load-data/MSC20250801001">测试load-data功能</a></p>
    <p><a href="/check-fix">检查修复状态</a></p>
    '''

@app.route('/check-fix')
def check_fix():
    """检查修复状态"""
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = []
        if 'size_row_count = len(size_data)' in content:
            checks.append("✅ size_row_count计算已修复")
        else:
            checks.append("❌ size_row_count计算未修复")
            
        if 'size_row_count=size_row_count' in content:
            checks.append("✅ size_row_count参数传递存在")
        else:
            checks.append("❌ size_row_count参数传递缺失")
            
        if 'size_1_position=size_1.get' in content:
            checks.append("✅ size_1变量存在")
        else:
            checks.append("❌ size_1变量缺失")
            
        if 'function_1_check=function_1.get' in content:
            checks.append("✅ function_1变量存在")
        else:
            checks.append("❌ function_1变量缺失")
        
        return '<h1>修复状态检查</h1><ul>' + ''.join(f'<li>{check}</li>' for check in checks) + '</ul>'
        
    except Exception as e:
        return f'<h1>检查失败</h1><p>{str(e)}</p>'

@app.route('/test-load-data/<sample_id>')
def test_load_data(sample_id):
    """测试load-data功能"""
    try:
        # 强制重新导入模块
        import importlib
        if 'blueprints.material_confirmation.load_data_handler' in sys.modules:
            importlib.reload(sys.modules['blueprints.material_confirmation.load_data_handler'])
        
        from blueprints.material_confirmation.load_data_handler import load_sample_data
        return load_sample_data(sample_id)
        
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        return f'''
        <h1>错误详情</h1>
        <h2>错误信息: {str(e)}</h2>
        <h3>完整堆栈跟踪:</h3>
        <pre>{error_details}</pre>
        <p><a href="/">返回首页</a></p>
        ''', 500

if __name__ == '__main__':
    print("=" * 60)
    print("启动独立测试应用")
    print("=" * 60)
    print("访问 http://127.0.0.1:5003/ 进行测试")
    print("这个应用会强制重新加载模块，确保使用最新代码")
    print("=" * 60)
    app.run(debug=True, port=5003, use_reloader=False)
