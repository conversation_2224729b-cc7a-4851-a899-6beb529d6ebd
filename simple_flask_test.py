#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Flask测试 - 验证load-data功能
"""

from flask import Flask, render_template_string
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

app = Flask(__name__)

@app.route('/test')
def test_template():
    """测试模板渲染"""
    template = """
    <h1>测试页面</h1>
    <p>size_row_count: {{ size_row_count }}</p>
    <p>size_1_position: {{ size_1_position }}</p>
    """
    
    return render_template_string(
        template,
        size_row_count=5,
        size_1_position="测试位置"
    )

@app.route('/test-load-data/<sample_id>')
def test_load_data(sample_id):
    """测试load-data功能"""
    try:
        from blueprints.material_confirmation.load_data_handler import load_sample_data
        return load_sample_data(sample_id)
    except Exception as e:
        return f"错误: {str(e)}", 500

if __name__ == '__main__':
    print("启动测试Flask应用...")
    print("测试URL:")
    print("- http://127.0.0.1:5001/test")
    print("- http://127.0.0.1:5001/test-load-data/MSC20250801001")
    app.run(debug=True, port=5001)
