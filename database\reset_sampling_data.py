#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重置抽样检验数据
"""

import sys
import os
import random
import json
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_config import get_db_connection
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'utils'))
from report_code_generator import generate_report_code

def reset_data():
    """重置数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🧹 清空现有数据...")
        cursor.execute("TRUNCATE TABLE sampling_inspection")
        print("✅ 抽样检验表已清空")
        
        # 生成示例数据
        print("\n📝 生成示例数据...")
        
        # 物料列表
        materials = [
            ('MAT001', '塑料外壳', 'ABS材质', '塑料', '黑色'),
            ('MAT002', '金属支架', '不锈钢304', '金属', '银色'),
            ('MAT003', '电路板', 'PCB-FR4', '电子', '绿色'),
            ('MAT004', '螺丝', 'M3*10', '紧固件', '银色'),
            ('MAT005', '橡胶垫片', '硅胶材质', '橡胶', '黑色')
        ]
        
        suppliers = ['供应商A', '供应商B', '供应商C', '供应商D', '供应商E']
        inspectors = ['张三', '李四', '王五', '赵六']
        
        # 问题类型
        issue_types = {
            '外观缺陷': ['划痕', '污渍', '变色', '毛刺'],
            '尺寸不良': ['长度超差', '宽度超差', '厚度超差', '孔径超差'],
            '功能异常': ['导通不良', '绝缘不良', '性能下降'],
            '包装问题': ['包装破损', '标识错误', '数量不符']
        }
        
        count = 0
        for i in range(50):  # 生成50条记录
            material = random.choice(materials)
            material_number = material[0]
            material_name = material[1]
            specification = material[2]
            material_type = material[3]
            color = material[4]
            
            supplier = random.choice(suppliers)
            inspector = random.choice(inspectors)
            
            # 随机生成日期（最近3个月）
            days_ago = random.randint(1, 90)
            inspection_date = datetime.now() - timedelta(days=days_ago)
            receipt_date = inspection_date - timedelta(days=random.randint(0, 3))
            
            total_quantity = random.randint(100, 1000)
            sample_quantity = random.randint(10, min(50, total_quantity // 10))
            defect_quantity = random.randint(0, min(5, sample_quantity))
            qualified_quantity = sample_quantity - defect_quantity
            
            # 生成问题点数据
            issues = []
            if defect_quantity > 0:
                num_issues = random.randint(1, min(3, defect_quantity))
                for _ in range(num_issues):
                    issue_type = random.choice(list(issue_types.keys()))
                    issue_desc = random.choice(issue_types[issue_type])
                    issues.append({
                        'type': issue_type,
                        'description': issue_desc
                    })
            
            # 生成报告编码
            report_code = generate_report_code('sampling', inspection_date)
            
            cursor.execute("""
                INSERT INTO sampling_inspection (
                    report_code, material_number, material_name, specification, material_type, color,
                    supplier, purchase_order, receipt_date, inspection_date,
                    total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                    defect_issues, inspector, created_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                report_code, material_number, material_name, specification, material_type, color,
                supplier, f'PO{inspection_date.strftime("%Y%m%d")}{i:03d}',
                receipt_date.strftime('%Y-%m-%d'),
                inspection_date.strftime('%Y-%m-%d'),
                total_quantity, sample_quantity, qualified_quantity, defect_quantity,
                json.dumps(issues, ensure_ascii=False) if issues else None,
                inspector, inspection_date
            ))
            count += 1
        
        conn.commit()
        print(f"✅ 成功生成 {count} 条抽样检验记录")
        return True
        
    except Exception as e:
        print(f"❌ 数据重置失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始重置抽样检验数据")
    print("=" * 50)
    
    if reset_data():
        print("\n" + "=" * 50)
        print("🎉 数据重置完成！")
    else:
        print("\n" + "=" * 50)
        print("❌ 数据重置失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
