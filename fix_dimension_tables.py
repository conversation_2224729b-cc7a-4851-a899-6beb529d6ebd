#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复dimension相关表的创建问题
"""

import mysql.connector
from config import Config

def create_dimension_tables():
    """创建dimension相关表"""
    try:
        config = Config()
        conn = mysql.connector.connect(
            host=config.DB_HOST,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME
        )
        cursor = conn.cursor()
        
        print("🔧 创建dimension相关表...")
        
        # 创建物料标准尺寸模板表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS material_dimension_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                material_number VARCHAR(50) NOT NULL COMMENT '物料编号',
                sequence_no INT NOT NULL COMMENT '序号',
                position_name VARCHAR(100) NOT NULL COMMENT '位置名称',
                nominal_dimension DECIMAL(10,4) NOT NULL COMMENT '标准尺寸',
                upper_tolerance DECIMAL(10,4) NOT NULL COMMENT '上公差',
                lower_tolerance DECIMAL(10,4) NOT NULL COMMENT '下公差',
                dimension_range VARCHAR(50) COMMENT '尺寸范围',
                is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                
                INDEX idx_material_number (material_number),
                INDEX idx_sequence_no (sequence_no),
                UNIQUE KEY uk_material_sequence (material_number, sequence_no)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='物料标准尺寸模板表'
        """)
        print("✅ material_dimension_templates表创建成功")
        
        # 创建尺寸测量记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS dimension_measurements (
                id INT AUTO_INCREMENT PRIMARY KEY,
                inspection_id INT NOT NULL COMMENT '检验记录ID',
                inspection_type ENUM('sampling', 'full') NOT NULL COMMENT '检验类型',
                material_number VARCHAR(50) NOT NULL COMMENT '物料编号',
                sequence_no INT NOT NULL COMMENT '序号',
                position_name VARCHAR(100) NOT NULL COMMENT '位置名称',
                nominal_dimension DECIMAL(10,4) COMMENT '标准尺寸',
                upper_tolerance DECIMAL(10,4) COMMENT '上公差',
                lower_tolerance DECIMAL(10,4) COMMENT '下公差',
                measured_value_1 DECIMAL(10,4) COMMENT '实测值1',
                measured_value_2 DECIMAL(10,4) COMMENT '实测值2',
                measured_value_3 DECIMAL(10,4) COMMENT '实测值3',
                measured_value_4 DECIMAL(10,4) COMMENT '实测值4',
                measured_value_5 DECIMAL(10,4) COMMENT '实测值5',
                result ENUM('合格', '不合格') COMMENT '测量结果',
                remarks TEXT COMMENT '备注',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                
                INDEX idx_inspection (inspection_id, inspection_type),
                INDEX idx_material_number (material_number),
                INDEX idx_sequence_no (sequence_no)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='尺寸测量记录表'
        """)
        print("✅ dimension_measurements表创建成功")
        
        conn.commit()
        
        # 创建示例模板数据
        print("\n📋 创建示例模板数据...")
        
        # 为ABC003创建模板数据
        templates = [
            ('ABC003', 1, '长度', 15.0000, 0.1000, -0.1000),
            ('ABC003', 2, '宽度', 10.0000, 0.0500, -0.0500),
            ('ABC003', 3, '厚度', 5.0000, 0.0200, -0.0200),
            
            # 为ABC001创建模板数据
            ('ABC001', 1, '螺栓长度', 50.0000, 0.2000, -0.2000),
            ('ABC001', 2, '螺栓直径', 10.0000, 0.0500, -0.0500),
            
            # 为ABC002创建模板数据
            ('ABC002', 1, '外壳长度', 100.0000, 0.2000, -0.2000),
            ('ABC002', 2, '外壳宽度', 80.0000, 0.1500, -0.1500),
        ]
        
        # 先删除现有数据
        cursor.execute("DELETE FROM material_dimension_templates WHERE material_number IN ('ABC003', 'ABC001', 'ABC002')")
        
        # 插入新数据
        for template in templates:
            material_number, sequence_no, position_name, nominal, upper_tol, lower_tol = template
            cursor.execute("""
                INSERT INTO material_dimension_templates (
                    material_number, sequence_no, position_name,
                    nominal_dimension, upper_tolerance, lower_tolerance
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """, (material_number, sequence_no, position_name, nominal, upper_tol, lower_tol))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"✅ 成功创建 {len(templates)} 条模板数据")
        print("✅ 所有dimension表创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def check_tables():
    """检查表是否创建成功"""
    try:
        config = Config()
        conn = mysql.connector.connect(
            host=config.DB_HOST,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            database=config.DB_NAME
        )
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'material_dimension_templates'")
        template_table = cursor.fetchone()
        
        cursor.execute("SHOW TABLES LIKE 'dimension_measurements'")
        measurement_table = cursor.fetchone()
        
        print("\n📊 表检查结果:")
        print(f"  material_dimension_templates: {'✅ 存在' if template_table else '❌ 不存在'}")
        print(f"  dimension_measurements: {'✅ 存在' if measurement_table else '❌ 不存在'}")
        
        if template_table:
            cursor.execute("SELECT COUNT(*) FROM material_dimension_templates")
            count = cursor.fetchone()[0]
            print(f"  模板数据条数: {count}")
        
        cursor.close()
        conn.close()
        
        return bool(template_table and measurement_table)
        
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始修复dimension相关表")
    print("=" * 50)
    
    if create_dimension_tables():
        check_tables()
        print("\n🎉 dimension功能修复完成！")
    else:
        print("\n❌ dimension功能修复失败！")
