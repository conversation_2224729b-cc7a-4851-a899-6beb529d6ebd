<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选控件交互测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h2 {
            color: #1976d2;
            margin-top: 0;
        }
        
        /* 复制原始筛选样式 */
        .filter-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            pointer-events: auto !important;
            position: relative !important;
            z-index: 5 !important;
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
            margin-bottom: 10px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
            flex: 1;
        }
        
        .filter-label {
            font-size: 12px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 4px;
            white-space: nowrap;
        }
        
        .filter-control {
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            outline: none;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 10 !important;
            cursor: text !important;
        }
        
        .filter-control:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        
        select.filter-control {
            cursor: pointer !important;
        }
        
        .filter-number-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .filter-operator {
            width: 60px;
            height: 32px;
            padding: 4px 6px;
            font-size: 11px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 10 !important;
            cursor: pointer !important;
        }
        
        .filter-number {
            width: 80px;
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            text-align: right;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 10 !important;
            cursor: text !important;
        }
        
        .filter-actions {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-left: auto;
        }
        
        .filter-btn {
            height: 32px;
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            white-space: nowrap;
        }
        
        .filter-btn-primary {
            background-color: #1976d2;
            color: white;
        }
        
        .filter-btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .log-output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin-top: 20px;
        }
        
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-button {
            background-color: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            background-color: #218838;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>筛选控件交互测试</h2>
            <p>此页面用于诊断和测试筛选控件无法输入和下拉的问题。</p>
        </div>
        
        <div class="test-section">
            <h3>问题诊断</h3>
            <button class="test-button" onclick="checkElementsExist()">检查元素是否存在</button>
            <button class="test-button" onclick="checkStyles()">检查计算样式</button>
            <button class="test-button" onclick="forceFixControls()">强制修复控件</button>
            <button class="test-button" onclick="testInteraction()">测试交互</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-group">
                    <label class="filter-label">报告编号</label>
                    <input type="text" class="filter-control" id="filter-report-code" placeholder="请输入报告编号">
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">供应商</label>
                    <select class="filter-control" id="filter-supplier">
                        <option value="">全部供应商</option>
                        <option value="供应商A">供应商A</option>
                        <option value="供应商B">供应商B</option>
                        <option value="供应商C">供应商C</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">检验类型</label>
                    <select class="filter-control" id="filter-inspection-type">
                        <option value="">全部类型</option>
                        <option value="来料检验">来料检验</option>
                        <option value="首件检验">首件检验</option>
                        <option value="巡检">巡检</option>
                        <option value="终检">终检</option>
                        <option value="特殊检验">特殊检验</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">不良率</label>
                    <div class="filter-number-group">
                        <select class="filter-operator" id="filter-defect-rate-op">
                            <option value="gt">大于</option>
                            <option value="gte">大于等于</option>
                            <option value="lt">小于</option>
                            <option value="lte">小于等于</option>
                            <option value="eq">等于</option>
                        </select>
                        <input type="number" class="filter-number" id="filter-defect-rate" placeholder="0.00" step="0.01" min="0" max="100">
                        <span style="font-size: 12px; color: #6c757d;">%</span>
                    </div>
                </div>
                
                <div class="filter-group">
                    <label class="filter-label">检验结果</label>
                    <select class="filter-control" id="filter-inspection-result">
                        <option value="">全部结果</option>
                        <option value="合格">合格</option>
                        <option value="不合格">不合格</option>
                        <option value="待检">待检</option>
                        <option value="让步接收">让步接收</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button type="button" class="filter-btn filter-btn-primary" id="apply-filters">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <button type="button" class="filter-btn filter-btn-secondary" id="reset-filters">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        let logOutput = document.getElementById('log-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logOutput.innerHTML = '';
        }
        
        function checkElementsExist() {
            log('=== 检查元素是否存在 ===');
            
            const elements = {
                'filter-report-code': document.getElementById('filter-report-code'),
                'filter-supplier': document.getElementById('filter-supplier'),
                'filter-inspection-type': document.getElementById('filter-inspection-type'),
                'filter-defect-rate-op': document.getElementById('filter-defect-rate-op'),
                'filter-defect-rate': document.getElementById('filter-defect-rate'),
                'filter-inspection-result': document.getElementById('filter-inspection-result')
            };
            
            Object.entries(elements).forEach(([id, element]) => {
                if (element) {
                    log(`✅ ${id}: 存在 (${element.tagName})`);
                } else {
                    log(`❌ ${id}: 不存在`);
                }
            });
        }
        
        function checkStyles() {
            log('=== 检查计算样式 ===');
            
            const elements = document.querySelectorAll('.filter-control, .filter-operator, .filter-number');
            elements.forEach((element, index) => {
                const styles = getComputedStyle(element);
                log(`元素 ${index + 1} (${element.id || element.className}):`);
                log(`  pointer-events: ${styles.pointerEvents}`);
                log(`  z-index: ${styles.zIndex}`);
                log(`  position: ${styles.position}`);
                log(`  display: ${styles.display}`);
                log(`  visibility: ${styles.visibility}`);
                log(`  cursor: ${styles.cursor}`);
                log(`  disabled: ${element.disabled}`);
            });
        }
        
        function forceFixControls() {
            log('=== 强制修复控件 ===');
            
            const elements = document.querySelectorAll('.filter-control, .filter-operator, .filter-number');
            elements.forEach((element, index) => {
                // 强制设置样式
                element.style.pointerEvents = 'auto';
                element.style.position = 'relative';
                element.style.zIndex = '10';
                element.style.display = 'block';
                element.style.visibility = 'visible';
                
                // 移除disabled属性
                element.removeAttribute('disabled');
                
                // 设置cursor
                if (element.tagName === 'SELECT') {
                    element.style.cursor = 'pointer';
                } else if (element.tagName === 'INPUT') {
                    element.style.cursor = 'text';
                }
                
                log(`修复元素 ${index + 1}: ${element.id || element.className} (${element.tagName})`);
            });
            
            log('控件修复完成');
        }
        
        function testInteraction() {
            log('=== 测试交互 ===');
            
            const elements = document.querySelectorAll('.filter-control, .filter-operator, .filter-number');
            elements.forEach((element, index) => {
                // 尝试聚焦
                try {
                    element.focus();
                    log(`✅ 元素 ${index + 1} 可以获得焦点`);
                    element.blur();
                } catch (e) {
                    log(`❌ 元素 ${index + 1} 无法获得焦点: ${e.message}`);
                }
                
                // 检查事件监听
                const hasClickListener = element.onclick !== null;
                log(`元素 ${index + 1} 点击事件: ${hasClickListener ? '有' : '无'}`);
            });
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始自动检查...');
            
            // 绑定事件监听器
            const elements = document.querySelectorAll('.filter-control, .filter-operator, .filter-number');
            elements.forEach((element, index) => {
                element.addEventListener('focus', function() {
                    log(`元素 ${index + 1} 获得焦点: ${this.id || this.className}`);
                });
                
                element.addEventListener('input', function() {
                    log(`元素 ${index + 1} 输入变化: ${this.value}`);
                });
                
                element.addEventListener('change', function() {
                    log(`元素 ${index + 1} 值变化: ${this.value}`);
                });
                
                element.addEventListener('click', function() {
                    log(`元素 ${index + 1} 被点击: ${this.id || this.className}`);
                });
            });
            
            // 延迟检查
            setTimeout(() => {
                checkElementsExist();
                checkStyles();
                forceFixControls();
            }, 100);
        });
    </script>
</body>
</html>
