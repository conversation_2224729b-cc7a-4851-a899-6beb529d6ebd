<tr class="custom-row-height dynamic-size-row" id="size-row-{{ row_number }}">
    <td class="align-center">{{ row_number }}</td>
    <td data-id="size-{{ row_number }}" data-image="true"><input type="text" name="size_{{ row_number }}_position" id="size-{{ row_number }}"></td>
    <td><input type="text" name="size_{{ row_number }}_value" id="size-{{ row_number }}-value"></td>
    <td><input type="text" name="size_{{ row_number }}_min" id="size-{{ row_number }}-min"></td>
    <td><input type="text" name="size_{{ row_number }}_max" id="size-{{ row_number }}-max"></td>
    <td><input type="text" name="size_{{ row_number }}_measure_1" id="size-{{ row_number }}-measure-1"></td>
    <td><input type="text" name="size_{{ row_number }}_measure_2" id="size-{{ row_number }}-measure-2"></td>
    <td><input type="text" name="size_{{ row_number }}_measure_3" id="size-{{ row_number }}-measure-3"></td>
    <td><input type="text" name="size_{{ row_number }}_measure_4" id="size-{{ row_number }}-measure-4"></td>
    <td><input type="text" name="size_{{ row_number }}_measure_5" id="size-{{ row_number }}-measure-5"></td>
    <td>
        <label><input type="radio" name="size_{{ row_number }}_check" value="合格"> 合格</label>
        <label><input type="radio" name="size_{{ row_number }}_check" value="不合格"> 不合格</label>
        <label><input type="radio" name="size_{{ row_number }}_check" value="AOD"> AOD</label>
    </td>
    <td colspan="2"><textarea name="size_{{ row_number }}_note" id="size-{{ row_number }}-note"></textarea></td>
</tr>
