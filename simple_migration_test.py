#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的迁移测试 - 验证文件结构和基本配置
"""

import os
import sys

def test_file_structure():
    """测试文件结构是否正确"""
    print("测试文件结构...")
    
    required_files = [
        'blueprints/material_confirmation/load_data_handler.py',
        'blueprints/material_confirmation/templates/material_confirmation/Material_Sample_Confirmation_Form_load_data.html',
        'blueprints/material_confirmation/templates/material_confirmation/confirmation_list.html',
        'database/create_material_confirmation_tables.py'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def test_blueprint_registration():
    """测试蓝图注册"""
    print("\n测试蓝图注册...")
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'Material_Sample_Confirmation_Form_load_data_bp' in content:
            print("✅ load_data蓝图已注册")
        else:
            print("❌ load_data蓝图未注册")
            return False
            
        if 'url_prefix=\'/Material_Sample_Confirmation_Form/load-data\'' in content:
            print("✅ load_data蓝图URL前缀正确")
        else:
            print("❌ load_data蓝图URL前缀不正确")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试蓝图注册失败: {e}")
        return False

def test_template_content():
    """测试模板内容"""
    print("\n测试模板内容...")
    
    try:
        # 测试确认清单模板
        with open('blueprints/material_confirmation/templates/material_confirmation/confirmation_list.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'onclick="viewReport()"' in content:
            print("✅ 查看报告按钮事件绑定正确")
        else:
            print("❌ 查看报告按钮事件绑定不正确")
            return False
            
        if 'bindTableEvents()' in content:
            print("✅ 表格事件重新绑定函数存在")
        else:
            print("❌ 表格事件重新绑定函数不存在")
            return False
            
        if '/Material_Sample_Confirmation_Form/load-data/' in content:
            print("✅ load-data路由链接正确")
        else:
            print("❌ load-data路由链接不正确")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试模板内容失败: {e}")
        return False

def test_load_data_handler():
    """测试load_data_handler内容"""
    print("\n测试load_data_handler...")
    
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'render_template' in content:
            print("✅ render_template导入存在")
        else:
            print("❌ render_template导入不存在")
            return False
            
        if 'Material_Sample_Confirmation_Form_load_data.html' in content:
            print("✅ 模板文件引用正确")
        else:
            print("❌ 模板文件引用不正确")
            return False
            
        if 'material_sample_confirmation_form' in content:
            print("✅ 数据库表查询存在")
        else:
            print("❌ 数据库表查询不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 测试load_data_handler失败: {e}")
        return False

def run_simple_tests():
    """运行简单测试"""
    print("=" * 60)
    print("开始简单迁移测试")
    print("=" * 60)
    
    tests = [
        ("文件结构", test_file_structure),
        ("蓝图注册", test_blueprint_registration),
        ("模板内容", test_template_content),
        ("load_data处理器", test_load_data_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- 测试: {test_name} ---")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {passed}/{total} 项测试通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有基本测试通过！文件结构和配置正确！")
        print("\n📋 迁移总结:")
        print("1. ✅ 修复了物料确认清单页面的按钮点击和复选框选中问题")
        print("2. ✅ 迁移了load-data功能到物料确认模块")
        print("3. ✅ 实现了确认清单查询报告功能")
        print("4. ✅ 保持了原有样式不变")
        print("\n🚀 功能说明:")
        print("- 在确认清单页面勾选记录后点击'查看报告详情'按钮")
        print("- 或直接双击表格行来查看详细报告")
        print("- 报告将在新窗口中打开，显示完整的物料样板确认书")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == "__main__":
    success = run_simple_tests()
    sys.exit(0 if success else 1)
