#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试修复结果
"""

def check_size_row_count_fix():
    """检查size_row_count修复"""
    print("检查size_row_count修复...")
    
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'size_row_count = len(size_data)' in content:
            print("✅ size_row_count计算已修复为基于size_data")
        else:
            print("❌ size_row_count计算未正确修复")
            return False
            
        if 'size_row_count=size_row_count' in content:
            print("✅ size_row_count参数传递存在")
        else:
            print("❌ size_row_count参数传递不存在")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_all_variables():
    """检查所有必要变量"""
    print("\n检查所有必要变量...")
    
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_vars = [
            'size_1_position=',
            'size_1_value=',
            'size_1_check=',
            'size_2_position=',
            'size_3_position=',
            'function_1_check=',
            'function_2_check=',
            'function_3_check=',
            'function_4_check=',
            'function_4_burnin=',
            'function_4_electrical=',
            'function_5_check=',
            'function_5_tests=',
            'function_5_other_test=',
            'function_6_check=',
            'function_6_other='
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ 缺少变量: {missing_vars}")
            return False
        else:
            print(f"✅ 所有 {len(required_vars)} 个必要变量都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    print("=" * 60)
    print("最终修复验证")
    print("=" * 60)
    
    tests = [
        check_size_row_count_fix,
        check_all_variables,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"验证完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有修复都已正确应用！")
        print("\n📋 修复总结:")
        print("1. ✅ 修复了size_row_count计算方式")
        print("2. ✅ 添加了所有必要的模板变量")
        print("3. ✅ 确保了变量传递的完整性")
        print("\n🚀 现在请执行以下步骤:")
        print("1. 停止当前Flask应用（Ctrl+C）")
        print("2. 清除缓存: python restart_app.py")
        print("3. 重新启动: python app.py")
        print("4. 测试访问: http://127.0.0.1:5000/material_confirmation/")
        print("5. 勾选记录并点击'查看报告详情'")
        print("\n应该不再出现 'size_row_count' is undefined 错误！")
    else:
        print("⚠️  部分检查失败，请检查相关问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
