<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单修复对比演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .demo-header {
            padding: 20px;
            background-color: #1976d2;
            color: white;
        }
        
        .demo-header h2 {
            margin: 0;
            font-size: 18px;
        }
        
        .demo-content {
            padding: 20px;
        }
        
        .navbar-demo {
            background-color: #1976d2;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .navbar-brand {
            color: white;
            font-weight: bold;
        }
        
        .navbar-right {
            position: relative;
        }
        
        .multi-function-btn {
            position: relative;
            display: inline-block;
        }
        
        .nav-icon {
            color: white;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 4px;
            background-color: rgba(255,255,255,0.1);
            cursor: pointer;
        }
        
        .nav-icon:hover {
            background-color: rgba(255,255,255,0.2);
        }
        
        .function-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            width: 160px;
            z-index: 1000;
            display: none;
            overflow: hidden;
        }
        
        .function-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            color: #333;
            text-decoration: none;
            font-size: 12px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .function-item:hover {
            background-color: #f8f9fa;
            color: #1976d2;
        }
        
        .function-item i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }
        
        .function-item:last-child {
            border-bottom: none;
        }
        
        /* 修复后的样式 */
        .fixed-version .multi-function-btn:hover + .function-dropdown {
            display: block !important;
        }
        
        .fixed-version .function-dropdown {
            position: fixed;
        }
        
        .problem-list {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .problem-list h4 {
            color: #c62828;
            margin-top: 0;
        }
        
        .solution-list {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .solution-list h4 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .comparison-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .status-bad {
            color: #c62828;
            font-weight: bold;
        }
        
        .status-good {
            color: #2e7d32;
            font-weight: bold;
        }
        
        .demo-instructions {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .demo-instructions h4 {
            color: #1976d2;
            margin-top: 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: #1976d2; margin-bottom: 30px;">右上角下拉菜单修复对比演示</h1>
        
        <!-- 问题版本演示 -->
        <div class="demo-section">
            <div class="demo-header">
                <h2>❌ 修复前版本（存在问题）</h2>
            </div>
            <div class="demo-content">
                <div class="problem-list">
                    <h4>存在的问题：</h4>
                    <ul>
                        <li>需要点击两次才能显示菜单</li>
                        <li>没有鼠标悬停自动显示功能</li>
                        <li>菜单定位可能异常</li>
                        <li>用户体验差</li>
                    </ul>
                </div>
                
                <div class="navbar-demo">
                    <div class="navbar-brand">品质中心管理系统</div>
                    <div class="navbar-right">
                        <div class="multi-function-btn">
                            <a href="#" class="nav-icon" onclick="toggleDropdownOld(event)">
                                <i class="fas fa-bars"></i>
                            </a>
                            <!-- 注意：这里是嵌套结构（问题版本） -->
                            <div class="function-dropdown" id="dropdown-old">
                                <a href="#" class="function-item">
                                    <i class="fas fa-chart-bar"></i> 数据面板
                                </a>
                                <a href="#" class="function-item">
                                    <i class="fas fa-plus"></i> 新增检验
                                </a>
                                <a href="#" class="function-item">
                                    <i class="fas fa-clipboard-list"></i> 检验记录
                                </a>
                                <a href="#" class="function-item">
                                    <i class="fas fa-cog"></i> 系统设置
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="demo-instructions">
                    <h4>测试说明：</h4>
                    <p>点击右上角的三条横线图标，体验问题版本的交互。注意需要多次点击才能显示，且没有悬停效果。</p>
                </div>
            </div>
        </div>
        
        <!-- 修复版本演示 -->
        <div class="demo-section fixed-version">
            <div class="demo-header">
                <h2>✅ 修复后版本（问题已解决）</h2>
            </div>
            <div class="demo-content">
                <div class="solution-list">
                    <h4>修复的功能：</h4>
                    <ul>
                        <li>点击一次立即显示菜单</li>
                        <li>鼠标悬停自动显示菜单</li>
                        <li>智能延迟隐藏（300ms）</li>
                        <li>菜单定位准确</li>
                        <li>多种交互方式支持</li>
                    </ul>
                </div>
                
                <div class="navbar-demo">
                    <div class="navbar-brand">品质中心管理系统</div>
                    <div class="navbar-right">
                        <div class="multi-function-btn" id="btn-container">
                            <a href="#" class="nav-icon" onclick="toggleDropdownNew(event)">
                                <i class="fas fa-bars"></i>
                            </a>
                        </div>
                        <!-- 注意：这里是平级结构（修复版本） -->
                        <div class="function-dropdown" id="dropdown-new">
                            <a href="#" class="function-item">
                                <i class="fas fa-chart-bar"></i> 数据面板
                            </a>
                            <a href="#" class="function-item">
                                <i class="fas fa-plus"></i> 新增检验
                            </a>
                            <a href="#" class="function-item">
                                <i class="fas fa-clipboard-list"></i> 检验记录
                            </a>
                            <a href="#" class="function-item">
                                <i class="fas fa-cog"></i> 系统设置
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="demo-instructions">
                    <h4>测试说明：</h4>
                    <p>将鼠标悬停在右上角的三条横线图标上，或点击图标，体验修复后的流畅交互。</p>
                </div>
            </div>
        </div>
        
        <!-- 对比表格 -->
        <div class="demo-section">
            <div class="demo-header">
                <h2>📊 功能对比表</h2>
            </div>
            <div class="demo-content">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能特性</th>
                            <th>修复前</th>
                            <th>修复后</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>点击响应</td>
                            <td class="status-bad">需要点击两次</td>
                            <td class="status-good">点击一次立即显示</td>
                        </tr>
                        <tr>
                            <td>鼠标悬停</td>
                            <td class="status-bad">不支持</td>
                            <td class="status-good">自动显示菜单</td>
                        </tr>
                        <tr>
                            <td>菜单隐藏</td>
                            <td class="status-bad">只能点击隐藏</td>
                            <td class="status-good">智能延迟隐藏</td>
                        </tr>
                        <tr>
                            <td>菜单定位</td>
                            <td class="status-bad">可能异常</td>
                            <td class="status-good">准确定位</td>
                        </tr>
                        <tr>
                            <td>用户体验</td>
                            <td class="status-bad">较差</td>
                            <td class="status-good">流畅直观</td>
                        </tr>
                        <tr>
                            <td>兼容性</td>
                            <td class="status-bad">依赖JavaScript</td>
                            <td class="status-good">CSS + JS双重保障</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 问题版本的JavaScript（模拟延迟和问题）
        let oldClickCount = 0;
        function toggleDropdownOld(e) {
            e.preventDefault();
            oldClickCount++;
            const dropdown = document.getElementById('dropdown-old');
            
            // 模拟需要点击两次的问题
            if (oldClickCount % 2 === 0) {
                const isVisible = dropdown.style.display === 'block';
                dropdown.style.display = isVisible ? 'none' : 'block';
            }
        }

        // 修复版本的JavaScript
        let isNewDropdownVisible = false;
        let hoverTimer = null;

        function toggleDropdownNew(e) {
            e.preventDefault();
            const dropdown = document.getElementById('dropdown-new');
            
            if (isNewDropdownVisible) {
                dropdown.style.display = 'none';
                isNewDropdownVisible = false;
            } else {
                dropdown.style.display = 'block';
                isNewDropdownVisible = true;
            }
        }

        // 修复版本的悬停事件
        document.addEventListener('DOMContentLoaded', function() {
            const btnContainer = document.getElementById('btn-container');
            const dropdown = document.getElementById('dropdown-new');

            if (btnContainer && dropdown) {
                // 鼠标进入按钮
                btnContainer.addEventListener('mouseenter', function() {
                    clearTimeout(hoverTimer);
                    dropdown.style.display = 'block';
                    isNewDropdownVisible = true;
                });

                // 鼠标离开按钮
                btnContainer.addEventListener('mouseleave', function() {
                    hoverTimer = setTimeout(function() {
                        dropdown.style.display = 'none';
                        isNewDropdownVisible = false;
                    }, 300);
                });

                // 鼠标进入菜单
                dropdown.addEventListener('mouseenter', function() {
                    clearTimeout(hoverTimer);
                });

                // 鼠标离开菜单
                dropdown.addEventListener('mouseleave', function() {
                    hoverTimer = setTimeout(function() {
                        dropdown.style.display = 'none';
                        isNewDropdownVisible = false;
                    }, 300);
                });
            }
        });
    </script>
</body>
</html>
