#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制重启Flask应用
"""

import os
import sys
import shutil
import subprocess

def clear_cache():
    """清除Python缓存"""
    print("清除Python缓存...")
    
    cache_dirs = []
    for root, dirs, files in os.walk('.'):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                cache_dirs.append(os.path.join(root, dir_name))
    
    for cache_dir in cache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"✅ 删除缓存目录: {cache_dir}")
        except Exception as e:
            print(f"⚠️  无法删除 {cache_dir}: {e}")
    
    # 删除.pyc文件
    pyc_files = []
    for root, dirs, files in os.walk('.'):
        for file_name in files:
            if file_name.endswith('.pyc'):
                pyc_files.append(os.path.join(root, file_name))
    
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"✅ 删除.pyc文件: {pyc_file}")
        except Exception as e:
            print(f"⚠️  无法删除 {pyc_file}: {e}")

def check_processes():
    """检查是否有Flask进程在运行"""
    print("检查Flask进程...")
    try:
        # Windows命令
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True)
        if 'python.exe' in result.stdout:
            print("⚠️  发现Python进程正在运行")
            print("请手动停止所有Python进程，然后重新运行")
        else:
            print("✅ 没有发现Python进程")
    except Exception as e:
        print(f"⚠️  无法检查进程: {e}")

def test_import():
    """测试导入是否正常"""
    print("测试模块导入...")
    try:
        from blueprints.material_confirmation.load_data_handler import Material_Sample_Confirmation_Form_load_data_bp
        print("✅ load_data_handler导入成功")
        
        from blueprints.material_confirmation.load_data_handler import load_sample_data
        print("✅ load_sample_data函数导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_app():
    """启动应用"""
    print("启动Flask应用...")
    try:
        import app
        print("✅ app.py导入成功")
        print("请手动运行: python app.py")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 60)
    print("Flask应用强制重启脚本")
    print("=" * 60)
    
    steps = [
        ("清除缓存", clear_cache),
        ("检查进程", check_processes),
        ("测试导入", test_import),
        ("准备启动", start_app),
    ]
    
    for step_name, step_func in steps:
        print(f"\n--- {step_name} ---")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name}失败: {e}")
    
    print("\n" + "=" * 60)
    print("重启准备完成！")
    print("\n📋 下一步操作:")
    print("1. 确保所有Python进程已停止")
    print("2. 运行: python app.py")
    print("3. 访问: http://127.0.0.1:5000/material_confirmation/")
    print("4. 测试load-data功能")
    print("=" * 60)

if __name__ == "__main__":
    main()
