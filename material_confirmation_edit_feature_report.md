# 物料确认修改功能实现报告

## 🎯 功能需求

实现物料确认页面 (`http://************:5000/material_confirmation/`) 的修改按钮功能，使其能够：
1. 将选中的记录数据读入到物料样板确认表单页面
2. 在表单中对数据和图片进行修改更新
3. 保存更新后的数据到数据库

## ✅ 实现的功能

### 1. 修改按钮跳转逻辑
**修改文件**: `blueprints/material_confirmation/templates/material_confirmation/confirmation_list.html`

**修改内容**:
```javascript
function editSelectedMaterialSample() {
    const selectedIds = getSelectedMaterialSampleIds();

    if (selectedIds.length !== 1) {
        alert('请选择一条记录进行修改');
        return;
    }
    
    // 跳转到Material_Sample_Confirmation_Form页面进行编辑
    const reportCode = selectedIds[0];
    const editUrl = `/Material_Sample_Confirmation_Form/edit/${reportCode}`;
    console.log('跳转到编辑页面:', editUrl);
    window.open(editUrl, "_blank");
}
```

### 2. 编辑页面路由
**修改文件**: `blueprints/material_confirmation/form_handler.py`

**新增路由**:
```python
@Material_Sample_Confirmation_Form_bp.route('/edit/<report_code>', methods=['GET'])
def edit_material_sample_confirmation_form(report_code):
    """编辑物料样板确认书"""
    # 查询基本信息、尺寸数据、外观数据、功能数据、问题点数据
    # 渲染编辑页面，传入所有数据
```

**功能特点**:
- 根据报告编码查询完整的表单数据
- 包括基本信息、尺寸检查、外观检查、功能检查、问题点等所有数据
- 将数据传递给模板进行预填充

### 3. 数据更新路由
**新增路由**:
```python
@Material_Sample_Confirmation_Form_bp.route('/update_material_sample/<report_code>', methods=['POST'])
def update_material_sample(report_code):
    """更新物料样板确认书数据"""
    # 更新基本信息、尺寸数据、外观数据、功能数据、问题点数据
```

**功能特点**:
- 支持更新所有表单数据
- 先删除现有的子表数据，再插入新数据
- 保持数据的完整性和一致性

### 4. 模板编辑模式支持
**修改文件**: `blueprints/material_confirmation/templates/material_confirmation/Material_Sample_Confirmation_Form.html`

**主要修改**:

#### 4.1 报告编码显示
```html
<span id="report-code">
    {% if edit_mode and report_code %}{{ report_code }}{% else %}表单提交后自动生成...{% endif %}
</span>
```

#### 4.2 提交按钮文本
```html
<button type="button" id="submit-btn">
    {% if edit_mode %}更新{% else %}提交{% endif %}
</button>
```

#### 4.3 输入字段预填充
所有输入字段都添加了编辑模式的值预填充：
```html
<input type="text" name="supplier" id="supplier" 
       value="{% if edit_mode and supplier %}{{ supplier }}{% endif %}" required>
```

#### 4.4 复选框状态预设
样板状态复选框根据数据库中的值进行预选：
```html
<input type="checkbox" id="trial-sample" 
       {% if edit_mode and sample_status_list and '试产样品' in sample_status_list %}checked{% endif %}>
```

### 5. JavaScript编辑模式处理
**主要功能**:

#### 5.1 智能提交URL选择
```javascript
// 检查是否为编辑模式
const isEditMode = document.getElementById('report-code').textContent.trim() !== '表单提交后自动生成...';
const reportCode = isEditMode ? document.getElementById('report-code').textContent.trim() : null;

// 根据模式选择不同的提交URL
const submitUrl = isEditMode 
    ? `/Material_Sample_Confirmation_Form/update_material_sample/${reportCode}`
    : '/Material_Sample_Confirmation_Form/submit_material_sample';
```

#### 5.2 数据表格预填充
```javascript
// 预填充尺寸数据
const sizeData = {{ size_data | tojson }};
sizeData.forEach((item, index) => {
    // 填充每一行的输入框
});

// 预填充外观数据、功能数据、问题点数据
```

#### 5.3 成功提示优化
```javascript
const successMessage = isEditMode 
    ? `更新成功！报告编码: ${result.report_code}` 
    : `提交成功！报告编码: ${result.report_code}`;
```

## 🔧 技术实现细节

### 1. 数据库查询优化
- 使用JOIN查询减少数据库访问次数
- 使用COALESCE处理NULL值
- 按序号排序确保数据顺序正确

### 2. 前端数据处理
- 使用Jinja2模板引擎进行服务端渲染
- JavaScript动态填充表格数据
- 智能检测编辑模式和新增模式

### 3. 错误处理
- 完善的异常捕获和错误提示
- 数据验证和完整性检查
- 用户友好的错误信息

## 🧪 测试流程

### 1. 基本功能测试
1. 访问物料确认页面: `http://************:5000/material_confirmation/`
2. 选择一条已有记录
3. 点击"修改"按钮
4. 验证是否正确跳转到编辑页面
5. 检查数据是否正确预填充

### 2. 编辑功能测试
1. 在编辑页面修改各种数据
2. 上传或修改图片
3. 点击"更新"按钮
4. 验证数据是否正确保存
5. 返回列表页面检查更新结果

### 3. 数据完整性测试
1. 测试所有类型的数据修改
2. 验证必填字段验证
3. 测试图片上传和删除
4. 检查数据库数据一致性

## 📋 支持的数据类型

### 基本信息
- ✅ 供应商/车间
- ✅ 送检日期
- ✅ 样板数量
- ✅ 检验员
- ✅ 物料料号
- ✅ 图号
- ✅ 物料名称
- ✅ 图纸版本
- ✅ 材质
- ✅ 表面处理
- ✅ 样板提供状态

### 检查数据
- ✅ 尺寸检查数据（位置、标准值、实测值、符合性、备注）
- ✅ 外观检查数据（检查项、结果、备注）
- ✅ 功能检查数据（检查项、结果、备注）
- ✅ 问题点数据（问题描述、图片）

### 结论数据
- ✅ 最终判定
- ✅ 意见
- ✅ 审核

## 🔄 后续优化建议

### 1. 图片编辑功能增强
- 支持编辑模式下的图片预览
- 实现图片的替换和删除功能
- 添加图片版本管理

### 2. 数据验证增强
- 添加更严格的数据格式验证
- 实现字段级别的实时验证
- 添加数据变更历史记录

### 3. 用户体验优化
- 添加保存草稿功能
- 实现数据变更提示
- 优化页面加载性能

## 📞 使用说明

### 修改现有记录
1. 进入物料确认页面
2. 在列表中选择要修改的记录（单选）
3. 点击"修改"按钮
4. 在打开的编辑页面中修改数据
5. 点击"更新"按钮保存修改

### 注意事项
- 每次只能选择一条记录进行修改
- 修改页面会在新标签页中打开
- 所有原有数据都会预填充到表单中
- 支持修改所有类型的数据和图片

---

**实现状态**: ✅ 已完成  
**测试状态**: 🧪 待验证  
**更新时间**: 2025年8月2日
