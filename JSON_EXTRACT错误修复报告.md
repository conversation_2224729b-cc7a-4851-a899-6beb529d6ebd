# JSON_EXTRACT错误修复报告

## 错误描述
在访问抽样检验页面时出现以下错误：
```
{
  "error": "3141 (22032): Invalid JSON text in argument 1 to function json_extract: \"Invalid value.\" at position 0."
}
```

**错误URL示例：**
```
http://127.0.0.1:5000/sampling_inspection/?start_date=2025-07-26&end_date=2025-08-02&date_type=inspection_date&quick_search=ABC001&material_number=&supplier=&purchase_order=&defect_rate_op=gt&defect_rate=&total_quantity_op=gt&total_quantity=&inspection_result=&receipt_start_date=2025-07-19&receipt_end_date=2025-08-02&page=1
```

## 问题分析

### 根本原因
错误发生在 `blueprints/incoming_inspection/routes.py` 第552行的快速搜索功能中：

```python
conditions.append("(material_number LIKE %s OR supplier LIKE %s OR material_name LIKE %s OR purchase_order LIKE %s OR JSON_EXTRACT(defect_issues, '$[*].description') LIKE %s)")
```

### 技术原因
1. **无效JSON数据**：数据库中 `sampling_inspection` 表的 `defect_issues` 字段包含无效的JSON数据
2. **缺少验证**：代码直接使用 `JSON_EXTRACT` 函数，没有先验证JSON的有效性
3. **MySQL限制**：MySQL的 `JSON_EXTRACT` 函数要求输入必须是有效的JSON格式

### 可能的无效数据类型
- 空字符串 `""`
- NULL值
- 纯文本字符串（非JSON格式）
- 格式错误的JSON字符串
- 包含特殊字符的字符串

## 修复方案

### 1. 代码修复
**修改文件：** `blueprints/incoming_inspection/routes.py` 和 `品控部 - 7.12.35/blueprints/incoming_inspection/routes.py`

**原代码：**
```python
conditions.append("(material_number LIKE %s OR supplier LIKE %s OR material_name LIKE %s OR purchase_order LIKE %s OR JSON_EXTRACT(defect_issues, '$[*].description') LIKE %s)")
```

**修复后：**
```python
conditions.append("(material_number LIKE %s OR supplier LIKE %s OR material_name LIKE %s OR purchase_order LIKE %s OR (defect_issues IS NOT NULL AND defect_issues != '' AND JSON_VALID(defect_issues) AND JSON_EXTRACT(defect_issues, '$[*].description') LIKE %s))")
```

**修复要点：**
- 添加 `defect_issues IS NOT NULL` 检查NULL值
- 添加 `defect_issues != ''` 检查空字符串
- 添加 `JSON_VALID(defect_issues)` 验证JSON有效性
- 只有在JSON有效时才执行 `JSON_EXTRACT`

### 2. 数据修复
创建了数据修复脚本 `fix_invalid_json_data.py` 来：
- 检查数据库中的无效JSON数据
- 尝试修复可修复的数据
- 将无法修复的数据设置为空JSON数组 `[]`

### 3. 测试验证
创建了测试脚本 `test_json_extract_fix.py` 来：
- 验证修复前的错误重现
- 测试修复后的查询功能
- 检查数据库中JSON数据的状态

## 修复步骤

### 步骤1：应用代码修复
已修复以下文件：
- `blueprints/incoming_inspection/routes.py` 第552行
- `品控部 - 7.12.35/blueprints/incoming_inspection/routes.py` 第552行

### 步骤2：运行数据修复脚本
```bash
python fix_invalid_json_data.py
```

### 步骤3：验证修复结果
```bash
python test_json_extract_fix.py
```

## 预防措施

### 1. 代码层面
- **JSON验证**：在使用JSON函数前始终验证数据有效性
- **错误处理**：添加适当的异常处理机制
- **数据验证**：在数据入库时验证JSON格式

### 2. 数据库层面
- **约束检查**：考虑添加JSON格式约束
- **定期检查**：定期检查数据完整性
- **备份策略**：确保数据修复前有备份

### 3. 开发规范
```python
# 推荐的JSON查询模式
def safe_json_query(field_name, json_path, search_value):
    return f"""
    ({field_name} IS NOT NULL 
     AND {field_name} != '' 
     AND JSON_VALID({field_name}) 
     AND JSON_EXTRACT({field_name}, '{json_path}') LIKE %s)
    """
```

## 影响范围

### 受影响的功能
- 抽样检验页面的快速搜索功能
- 包含缺陷问题描述的搜索查询
- 任何使用 `JSON_EXTRACT` 查询 `defect_issues` 字段的功能

### 修复后的改进
- 搜索功能更加稳定可靠
- 避免因无效JSON数据导致的系统错误
- 提高了数据查询的容错性

## 测试建议

### 功能测试
1. **正常搜索**：测试各种搜索条件的正常功能
2. **边界测试**：测试空值、特殊字符等边界情况
3. **性能测试**：验证修复后的查询性能

### 数据测试
1. **JSON有效性**：确认所有JSON数据格式正确
2. **搜索准确性**：验证搜索结果的准确性
3. **数据完整性**：确认修复过程中没有数据丢失

## 总结

通过添加JSON有效性验证和数据修复，成功解决了 `JSON_EXTRACT` 函数报错的问题。修复方案具有以下特点：

1. **安全性**：不会因无效数据导致系统错误
2. **兼容性**：保持原有功能的完整性
3. **可维护性**：提供了清晰的错误处理逻辑
4. **可扩展性**：为未来类似问题提供了解决模板

修复后，抽样检验页面的搜索功能将能够正常工作，不再出现JSON相关的错误。
