#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Check database tables structure
"""

from db_config import get_db_connection

def check_tables():
    """Check available tables and their structure"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute('SHOW TABLES')
        tables = cursor.fetchall()
        print('Available tables:')
        for table in tables:
            print(f'  - {table[0]}')
        
        # Check sampling inspection related tables
        sampling_tables = []
        for table in tables:
            if 'sampling' in table[0] or 'inspection' in table[0]:
                sampling_tables.append(table[0])
        
        print('\nInspection related tables:')
        for table_name in sampling_tables:
            print(f'\n=== {table_name} ===')
            cursor.execute(f'DESCRIBE {table_name}')
            columns = cursor.fetchall()
            for col in columns:
                print(f'  {col[0]} - {col[1]}')
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'Error: {e}')

if __name__ == "__main__":
    check_tables()
