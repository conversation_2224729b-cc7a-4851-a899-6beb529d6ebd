#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查dimension相关表结构
"""

from db_config import get_db_connection

def check_dimension_tables():
    """检查dimension相关表结构"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查是否存在full_inspection表
        cursor.execute('SHOW TABLES LIKE "full_inspection"')
        full_table = cursor.fetchall()
        
        if full_table:
            print('❌ full_inspection表仍然存在')
        else:
            print('✅ full_inspection表已删除')
        
        # 检查dimension相关表
        cursor.execute('SHOW TABLES LIKE "%dimension%"')
        dimension_tables = cursor.fetchall()
        print('\nDimension相关表:')
        for table in dimension_tables:
            print(f'  - {table[0]}')
        
        # 检查material_dimension_templates表
        cursor.execute('SHOW TABLES LIKE "material_dimension_templates"')
        template_table = cursor.fetchall()
        
        if template_table:
            print('\n✅ material_dimension_templates表存在')
        else:
            print('\n❌ material_dimension_templates表不存在')
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'错误: {e}')

if __name__ == "__main__":
    check_dimension_tables()
