from flask import request, jsonify
from . import material_confirmation_bp
from db_config import get_db_connection
import json
from datetime import datetime

# API路由可以在这里添加
# 例如：删除、编辑、导出等功能的API接口

@material_confirmation_bp.route('/api/delete', methods=['POST'])
def delete_confirmation():
    """删除物料确认记录"""
    try:
        data = request.get_json()
        report_codes = data.get('report_codes', [])
        
        if not report_codes:
            return jsonify({"success": False, "error": "没有提供要删除的记录"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        deleted_count = 0
        for report_code in report_codes:
            # 先获取form_id
            cursor.execute("SELECT id FROM material_sample_confirmation_form WHERE report_code = %s", (report_code,))
            result = cursor.fetchone()
            
            if result:
                form_id = result[0]
                
                # 删除相关的问题记录
                cursor.execute("DELETE FROM material_sample_questions WHERE form_id = %s", (form_id,))
                
                # 删除相关的尺寸数据
                cursor.execute("DELETE FROM material_sample_size_data WHERE form_id = %s", (form_id,))
                
                # 删除相关的外观检查
                cursor.execute("DELETE FROM material_sample_appearance WHERE form_id = %s", (form_id,))
                
                # 删除相关的功能检查
                cursor.execute("DELETE FROM material_sample_function WHERE form_id = %s", (form_id,))
                
                # 删除主记录
                cursor.execute("DELETE FROM material_sample_confirmation_form WHERE id = %s", (form_id,))
                
                deleted_count += 1
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return jsonify({
            "success": True, 
            "message": f"成功删除 {deleted_count} 条记录"
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@material_confirmation_bp.route('/api/export', methods=['POST'])
def export_confirmations():
    """导出物料确认记录"""
    try:
        data = request.get_json()
        report_codes = data.get('report_codes', [])
        
        if not report_codes:
            return jsonify({"success": False, "error": "没有提供要导出的记录"}), 400
        
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        export_data = []
        for report_code in report_codes:
            # 获取基本信息
            cursor.execute("""
                SELECT * FROM material_sample_confirmation_form 
                WHERE report_code = %s
            """, (report_code,))
            
            basic_info = cursor.fetchone()
            if basic_info:
                # 获取问题点
                cursor.execute("""
                    SELECT question_number, question_text, image_path
                    FROM material_sample_questions 
                    WHERE form_id = %s 
                    ORDER BY question_number
                """, (basic_info['id'],))
                
                questions = cursor.fetchall()
                basic_info['questions'] = questions
                export_data.append(basic_info)
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "success": True,
            "data": export_data,
            "count": len(export_data)
        })
        
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
