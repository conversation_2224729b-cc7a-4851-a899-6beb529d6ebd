#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成100条待检清单和检验记录数据
"""

import random
import json
from datetime import datetime, timedelta, date
from db_config import get_db_connection

def generate_report_code(prefix='SAMPLING'):
    """生成检验报告编号"""
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    random_suffix = random.randint(100, 999)
    return f"{prefix}{timestamp}{random_suffix}"

def get_materials_data():
    """获取物料基础数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        cursor.execute("""
            SELECT material_number, material_name, specification, material_type, 
                   color, material_category, inspection_type, unit
            FROM materials 
            WHERE material_number IS NOT NULL AND material_name IS NOT NULL
            LIMIT 50
        """)
        materials = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        if not materials:
            # 如果没有物料数据，创建一些基础数据
            return create_basic_materials()
        
        return materials
        
    except Exception as e:
        print(f"获取物料数据失败: {e}")
        return create_basic_materials()

def create_basic_materials():
    """创建基础物料数据"""
    materials = []
    
    # 电子元件
    for i in range(1, 21):
        materials.append({
            'material_number': f'ABC{i:03d}',
            'material_name': f'电阻器-{i}K欧',
            'specification': f'1/4W ±5% {i}KΩ',
            'material_type': '电子元件',
            'color': '蓝色',
            'material_category': '原材料',
            'inspection_type': '抽样',
            'unit': '个'
        })
    
    # 机械零件
    for i in range(1, 16):
        materials.append({
            'material_number': f'DEF{i:03d}',
            'material_name': f'螺栓M{i}',
            'specification': f'M{i}×{i*5}mm',
            'material_type': '机械零件',
            'color': '银色',
            'material_category': '原材料',
            'inspection_type': '抽样',
            'unit': '个'
        })
    
    # 塑料件
    for i in range(1, 11):
        materials.append({
            'material_number': f'GHI{i:03d}',
            'material_name': f'塑料外壳-{i}',
            'specification': f'{i*10}×{i*8}×{i*2}mm',
            'material_type': '塑料件',
            'color': '黑色',
            'material_category': '半成品',
            'inspection_type': '全检',
            'unit': '个'
        })
    
    return materials

def generate_pending_inspections_data(count=100):
    """生成待检清单数据"""
    try:
        print(f"开始生成{count}条待检清单数据...")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取物料数据
        materials = get_materials_data()
        if not materials:
            print("❌ 无法获取物料数据")
            return False
        
        # 供应商列表
        suppliers = [
            '深圳市华强电子有限公司', '上海精密制造股份有限公司', '北京科技发展集团',
            '广州电子元件厂', '苏州工业园区制造公司', '东莞精密零件有限公司',
            '杭州电子科技公司', '成都制造业集团', '武汉精密工业公司', '西安电子有限公司'
        ]
        
        # 检验员列表
        inspectors = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
        
        # 创建批次
        batch_name = f"批次_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute("""
            INSERT INTO pending_inspection_batches (batch_name, inspection_type, created_by)
            VALUES (%s, %s, %s)
        """, (batch_name, 'sampling', 'system'))
        batch_id = cursor.lastrowid
        
        success_count = 0
        
        for i in range(count):
            try:
                # 随机选择物料
                material = random.choice(materials)
                
                # 随机生成数据
                supplier = random.choice(suppliers)
                inspector = random.choice(inspectors)
                
                # 日期生成（最近30天内）
                days_ago = random.randint(1, 30)
                arrival_date = datetime.now() - timedelta(days=days_ago)
                planned_inspection_date = arrival_date + timedelta(days=random.randint(1, 3))
                
                # 数量和批次号
                incoming_quantity = random.randint(100, 5000)
                batch_number = f"LOT{arrival_date.strftime('%Y%m%d')}{i:03d}"
                
                # 采购单号
                purchase_order = f"PO{arrival_date.strftime('%Y%m%d')}{random.randint(100, 999)}"
                
                # 检验类型映射
                inspection_type_map = {
                    '抽样': 'sampling',
                    '全检': 'full', 
                    '免检': 'exempt'
                }
                inspection_type = inspection_type_map.get(material.get('inspection_type', '抽样'), 'sampling')
                
                # 状态随机分配
                status_weights = [
                    ('pending', 0.4),      # 40% 待检
                    ('in_progress', 0.3),  # 30% 检验中
                    ('completed', 0.2),    # 20% 已完成
                    ('cancelled', 0.1)     # 10% 已取消
                ]
                status = random.choices([s[0] for s in status_weights], 
                                      weights=[s[1] for s in status_weights])[0]
                
                # 备注
                remarks_list = [
                    '正常来料检验', '紧急订单', '供应商首次供货', '质量问题跟踪',
                    '批量生产前确认', '客户指定供应商', '成本优化项目', '新产品试制'
                ]
                remarks = random.choice(remarks_list) if random.random() > 0.3 else None
                
                # 插入待检物料记录
                cursor.execute("""
                    INSERT INTO pending_inspections (
                        material_code, material_name, specification, supplier_name, purchase_order,
                        incoming_quantity, unit, inspection_type, status, batch_number,
                        arrival_date, planned_inspection_date, inspector, remarks,
                        batch_id, created_by
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    material['material_number'],
                    material['material_name'],
                    material['specification'],
                    supplier,
                    purchase_order,
                    incoming_quantity,
                    material.get('unit', '个'),
                    inspection_type,
                    status,
                    batch_number,
                    arrival_date.date(),
                    planned_inspection_date.date(),
                    inspector,
                    remarks,
                    batch_id,
                    'system'
                ))
                
                success_count += 1
                
            except Exception as e:
                print(f"生成第{i+1}条记录失败: {e}")
                continue
        
        # 更新批次统计
        cursor.execute("""
            UPDATE pending_inspection_batches 
            SET total_items = %s, pending_items = (
                SELECT COUNT(*) FROM pending_inspections 
                WHERE batch_id = %s AND status = 'pending'
            ), in_progress_items = (
                SELECT COUNT(*) FROM pending_inspections 
                WHERE batch_id = %s AND status = 'in_progress'
            ), completed_items = (
                SELECT COUNT(*) FROM pending_inspections 
                WHERE batch_id = %s AND status = 'completed'
            )
            WHERE id = %s
        """, (success_count, batch_id, batch_id, batch_id, batch_id))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"✅ 成功生成 {success_count} 条待检清单记录")
        return True

    except Exception as e:
        print(f"❌ 生成待检清单数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def generate_inspection_records_data(count=100):
    """生成检验记录数据"""
    try:
        print(f"开始生成{count}条检验记录数据...")

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        # 获取物料数据
        materials = get_materials_data()
        if not materials:
            print("❌ 无法获取物料数据")
            return False

        # 供应商列表
        suppliers = [
            '深圳市华强电子有限公司', '上海精密制造股份有限公司', '北京科技发展集团',
            '广州电子元件厂', '苏州工业园区制造公司', '东莞精密零件有限公司',
            '杭州电子科技公司', '成都制造业集团', '武汉精密工业公司', '西安电子有限公司'
        ]

        # 检验员列表
        inspectors = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

        # 问题类型定义
        issue_types = {
            '外观问题': ['划痕', '变色', '污渍', '破损', '毛刺'],
            '尺寸问题': ['超差', '偏小', '偏大', '不圆', '不平'],
            '功能问题': ['不导通', '阻值超差', '绝缘不良', '接触不良', '性能下降'],
            '包装问题': ['包装破损', '标识错误', '数量不符', '混料', '受潮']
        }

        success_count = 0

        for i in range(count):
            try:
                # 随机选择物料
                material = random.choice(materials)

                # 随机生成数据
                supplier = random.choice(suppliers)
                inspector = random.choice(inspectors)

                # 日期生成（最近60天内）
                days_ago = random.randint(1, 60)
                inspection_date = datetime.now() - timedelta(days=days_ago)
                receipt_date = inspection_date - timedelta(days=random.randint(0, 3))

                # 数量数据
                total_quantity = random.randint(100, 5000)
                sample_quantity = random.randint(10, min(200, total_quantity // 5))

                # 随机生成缺陷数量（大部分是合格的）
                defect_probability = random.random()
                if defect_probability < 0.7:  # 70% 完全合格
                    defect_quantity = 0
                elif defect_probability < 0.9:  # 20% 少量缺陷
                    defect_quantity = random.randint(1, min(3, sample_quantity // 10))
                else:  # 10% 较多缺陷
                    defect_quantity = random.randint(1, min(sample_quantity // 3, 10))

                qualified_quantity = sample_quantity - defect_quantity

                # 生成问题点数据
                defect_issues_data = []
                if defect_quantity > 0:
                    num_issue_types = random.randint(1, min(3, defect_quantity))
                    selected_types = random.sample(list(issue_types.keys()), num_issue_types)

                    for issue_type in selected_types:
                        issue_desc = random.choice(issue_types[issue_type])
                        defect_issues_data.append({
                            'type': issue_type,
                            'description': issue_desc,
                            'quantity': random.randint(1, defect_quantity)
                        })

                defect_issues_json = json.dumps(defect_issues_data, ensure_ascii=False) if defect_issues_data else None

                # 生成报告编码
                report_code = generate_report_code('SAMPLING')

                # 采购单号和批次号
                purchase_order = f"PO{inspection_date.strftime('%Y%m%d')}{random.randint(100, 999)}"
                batch_number = f"LOT{inspection_date.strftime('%Y%m%d')}{i:03d}"

                # 插入抽样检验记录
                cursor.execute("""
                    INSERT INTO sampling_inspection (
                        report_code, material_number, material_name, specification,
                        material_type, color, supplier, purchase_order,
                        receipt_date, inspection_date, total_quantity,
                        sample_quantity, defect_quantity, qualified_quantity,
                        defect_issues, inspector, created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """, (
                    report_code,
                    material['material_number'],
                    material['material_name'],
                    material['specification'],
                    material['material_type'],
                    material['color'],
                    supplier,
                    purchase_order,
                    receipt_date.date(),
                    inspection_date.date(),
                    total_quantity,
                    sample_quantity,
                    defect_quantity,
                    qualified_quantity,
                    defect_issues_json,
                    inspector,
                    inspection_date
                ))

                success_count += 1

            except Exception as e:
                print(f"生成第{i+1}条检验记录失败: {e}")
                continue

        conn.commit()
        cursor.close()
        conn.close()

        print(f"✅ 成功生成 {success_count} 条检验记录")
        return True

    except Exception as e:
        print(f"❌ 生成检验记录数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("🚀 开始生成待检清单和检验记录数据")
    print("=" * 60)

    # 1. 生成待检清单数据
    print("\n📋 第一步：生成待检清单数据")
    if not generate_pending_inspections_data(100):
        print("❌ 待检清单数据生成失败，程序退出")
        return False

    # 2. 生成检验记录数据
    print("\n🔍 第二步：生成检验记录数据")
    if not generate_inspection_records_data(100):
        print("❌ 检验记录数据生成失败，程序退出")
        return False

    print("=" * 60)
    print("🎉 数据生成完成！")
    print("\n📊 生成的数据统计:")
    print("  - 待检清单记录: 100条")
    print("  - 检验记录: 100条")
    print("  - 总计: 200条记录")
    print("\n💡 数据特点:")
    print("  - 时间范围: 最近30-60天")
    print("  - 包含多种物料类型")
    print("  - 包含多种检验状态")
    print("  - 包含真实的问题点数据")
    print("  - 支持系统功能测试")

    return True

if __name__ == "__main__":
    main()
