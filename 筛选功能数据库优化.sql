-- 筛选功能数据库优化SQL脚本
-- 为抽样检验记录的筛选功能添加索引，提高查询性能

-- ============================================
-- 1. 筛选字段索引
-- ============================================

-- 供应商字段索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_sampling_supplier 
ON sampling_inspection(supplier);

-- 检验类型字段索引
CREATE INDEX IF NOT EXISTS idx_sampling_inspection_type 
ON sampling_inspection(inspection_type);

-- 检验结果字段索引
CREATE INDEX IF NOT EXISTS idx_sampling_inspection_result 
ON sampling_inspection(inspection_result);

-- 报告编码字段索引（支持模糊搜索）
CREATE INDEX IF NOT EXISTS idx_sampling_report_code 
ON sampling_inspection(report_code);

-- ============================================
-- 2. 复合索引（用于组合筛选）
-- ============================================

-- 供应商 + 检验类型复合索引
CREATE INDEX IF NOT EXISTS idx_sampling_supplier_type 
ON sampling_inspection(supplier, inspection_type);

-- 检验类型 + 检验结果复合索引
CREATE INDEX IF NOT EXISTS idx_sampling_type_result 
ON sampling_inspection(inspection_type, inspection_result);

-- 供应商 + 检验结果复合索引
CREATE INDEX IF NOT EXISTS idx_sampling_supplier_result 
ON sampling_inspection(supplier, inspection_result);

-- ============================================
-- 3. 日期相关索引优化
-- ============================================

-- 检验日期索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_sampling_inspection_date 
ON sampling_inspection(inspection_date);

-- 来料日期索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_sampling_receipt_date 
ON sampling_inspection(receipt_date);

-- 检验日期 + 供应商复合索引（常用组合）
CREATE INDEX IF NOT EXISTS idx_sampling_date_supplier 
ON sampling_inspection(inspection_date, supplier);

-- ============================================
-- 4. 不良率计算相关索引
-- ============================================

-- 检验数量和不良数量复合索引（用于不良率计算）
CREATE INDEX IF NOT EXISTS idx_sampling_quantities 
ON sampling_inspection(sample_quantity, defect_quantity);

-- ============================================
-- 5. 查询性能验证
-- ============================================

-- 验证筛选选项查询性能
EXPLAIN QUERY PLAN 
SELECT DISTINCT supplier 
FROM sampling_inspection 
WHERE supplier IS NOT NULL AND supplier != '' 
ORDER BY supplier;

EXPLAIN QUERY PLAN 
SELECT DISTINCT inspection_type 
FROM sampling_inspection 
WHERE inspection_type IS NOT NULL AND inspection_type != '' 
ORDER BY inspection_type;

EXPLAIN QUERY PLAN 
SELECT DISTINCT inspection_result 
FROM sampling_inspection 
WHERE inspection_result IS NOT NULL AND inspection_result != '' 
ORDER BY inspection_result;

-- 验证组合筛选查询性能
EXPLAIN QUERY PLAN 
SELECT * FROM sampling_inspection 
WHERE supplier = '测试供应商'
  AND inspection_type = '来料检验'
  AND inspection_result = '合格'
  AND inspection_date BETWEEN '2024-01-01' AND '2024-12-31'
ORDER BY inspection_date DESC
LIMIT 20;

-- ============================================
-- 6. 索引维护建议
-- ============================================

-- 查看索引使用情况（SQLite特定）
-- PRAGMA index_list('sampling_inspection');
-- PRAGMA index_info('idx_sampling_supplier');

-- 分析表统计信息
ANALYZE sampling_inspection;

-- ============================================
-- 7. 性能测试查询
-- ============================================

-- 测试单字段筛选性能
SELECT COUNT(*) FROM sampling_inspection WHERE supplier = '特定供应商';
SELECT COUNT(*) FROM sampling_inspection WHERE inspection_type = '来料检验';
SELECT COUNT(*) FROM sampling_inspection WHERE inspection_result = '合格';

-- 测试组合筛选性能
SELECT COUNT(*) FROM sampling_inspection 
WHERE supplier = '特定供应商' 
  AND inspection_type = '来料检验';

SELECT COUNT(*) FROM sampling_inspection 
WHERE inspection_type = '来料检验' 
  AND inspection_result = '合格';

-- 测试模糊搜索性能
SELECT COUNT(*) FROM sampling_inspection 
WHERE report_code LIKE '%ABC%';

-- 测试不良率筛选性能
SELECT COUNT(*) FROM sampling_inspection 
WHERE sample_quantity > 0 
  AND (defect_quantity / sample_quantity * 100) > 5.0;

-- ============================================
-- 8. 数据清理建议
-- ============================================

-- 检查空值和无效数据
SELECT 
    COUNT(*) as total_records,
    COUNT(supplier) as supplier_not_null,
    COUNT(CASE WHEN supplier != '' THEN 1 END) as supplier_not_empty,
    COUNT(inspection_type) as type_not_null,
    COUNT(CASE WHEN inspection_type != '' THEN 1 END) as type_not_empty,
    COUNT(inspection_result) as result_not_null,
    COUNT(CASE WHEN inspection_result != '' THEN 1 END) as result_not_empty
FROM sampling_inspection;

-- 查看各字段的唯一值数量
SELECT 'supplier' as field, COUNT(DISTINCT supplier) as unique_count
FROM sampling_inspection 
WHERE supplier IS NOT NULL AND supplier != ''
UNION ALL
SELECT 'inspection_type' as field, COUNT(DISTINCT inspection_type) as unique_count
FROM sampling_inspection 
WHERE inspection_type IS NOT NULL AND inspection_type != ''
UNION ALL
SELECT 'inspection_result' as field, COUNT(DISTINCT inspection_result) as unique_count
FROM sampling_inspection 
WHERE inspection_result IS NOT NULL AND inspection_result != '';

-- ============================================
-- 9. 缓存表建议（可选）
-- ============================================

-- 如果筛选选项查询频繁，可以考虑创建缓存表
-- CREATE TABLE IF NOT EXISTS filter_options_cache (
--     field_name VARCHAR(50),
--     option_value VARCHAR(255),
--     last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     PRIMARY KEY (field_name, option_value)
-- );

-- 缓存表的更新触发器示例
-- CREATE TRIGGER IF NOT EXISTS update_filter_cache_supplier
-- AFTER INSERT OR UPDATE OR DELETE ON sampling_inspection
-- BEGIN
--     DELETE FROM filter_options_cache WHERE field_name = 'supplier';
--     INSERT INTO filter_options_cache (field_name, option_value)
--     SELECT 'supplier', DISTINCT supplier 
--     FROM sampling_inspection 
--     WHERE supplier IS NOT NULL AND supplier != '';
-- END;

-- ============================================
-- 10. 监控查询
-- ============================================

-- 监控慢查询（需要启用查询日志）
-- 定期检查筛选功能相关查询的执行时间
-- 根据实际使用情况调整索引策略

-- 示例：检查最常用的筛选组合
SELECT 
    supplier,
    inspection_type,
    inspection_result,
    COUNT(*) as usage_count
FROM sampling_inspection 
GROUP BY supplier, inspection_type, inspection_result
ORDER BY usage_count DESC
LIMIT 10;
