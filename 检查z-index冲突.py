#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查项目中的z-index设置，避免层级冲突
"""

import os
import re
from collections import defaultdict

def find_z_index_values(file_path):
    """在文件中查找z-index值"""
    z_index_pattern = r'z-index\s*:\s*(\d+)'
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for line_num, line in enumerate(lines, 1):
                matches = re.finditer(z_index_pattern, line, re.IGNORECASE)
                for match in matches:
                    z_value = int(match.group(1))
                    context = line.strip()
                    results.append({
                        'file': file_path,
                        'line': line_num,
                        'z_index': z_value,
                        'context': context
                    })
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {e}")
    
    return results

def scan_project_for_z_index():
    """扫描项目中的z-index设置"""
    z_index_data = []
    
    # 要扫描的文件类型和路径
    scan_paths = [
        ('static/css/style.css', 'CSS'),
        ('templates/base.html', 'HTML'),
        ('blueprints/incoming_inspection/templates/sampling_inspection.html', 'HTML'),
    ]
    
    for file_path, file_type in scan_paths:
        if os.path.exists(file_path):
            print(f"扫描 {file_path}...")
            results = find_z_index_values(file_path)
            for result in results:
                result['type'] = file_type
                z_index_data.append(result)
        else:
            print(f"文件不存在: {file_path}")
    
    return z_index_data

def analyze_z_index_conflicts(z_index_data):
    """分析z-index冲突"""
    # 按z-index值分组
    by_z_index = defaultdict(list)
    for item in z_index_data:
        by_z_index[item['z_index']].append(item)
    
    print("\n=== Z-INDEX 分析报告 ===")
    
    # 显示所有z-index值
    print("\n所有z-index值（按值排序）:")
    for z_value in sorted(by_z_index.keys()):
        items = by_z_index[z_value]
        print(f"\nz-index: {z_value}")
        for item in items:
            print(f"  - {item['file']}:{item['line']} ({item['type']})")
            print(f"    {item['context']}")
    
    # 检查冲突
    print("\n=== 潜在冲突分析 ===")
    conflicts_found = False
    
    for z_value, items in by_z_index.items():
        if len(items) > 1:
            # 检查是否是真正的冲突
            contexts = [item['context'].lower() for item in items]
            
            # 如果涉及模态框和其他元素，可能有冲突
            has_modal = any('modal' in ctx for ctx in contexts)
            has_dropdown = any('dropdown' in ctx or 'function' in ctx for ctx in contexts)
            has_button = any('button' in ctx or 'icon' in ctx for ctx in contexts)
            
            if (has_modal and (has_dropdown or has_button)) or (z_value >= 9999):
                conflicts_found = True
                print(f"\n⚠️  潜在冲突 - z-index: {z_value}")
                for item in items:
                    print(f"  - {item['file']}:{item['line']}")
                    print(f"    {item['context']}")
                
                if has_modal and has_button:
                    print("  建议: 模态框应该有更高的z-index值")
    
    if not conflicts_found:
        print("✅ 未发现明显的z-index冲突")
    
    return by_z_index

def suggest_z_index_hierarchy():
    """建议z-index层级结构"""
    print("\n=== 建议的Z-INDEX层级结构 ===")
    hierarchy = [
        (1, "基础内容层"),
        (10, "浮动元素"),
        (100, "工具提示"),
        (1000, "下拉菜单"),
        (10000, "模态框"),
        (100000, "最高优先级提示")
    ]
    
    for z_value, description in hierarchy:
        print(f"z-index: {z_value:6d} - {description}")

def main():
    print("=== Z-INDEX 冲突检查工具 ===")
    print("检查项目中的z-index设置，识别潜在冲突\n")
    
    # 扫描项目
    z_index_data = scan_project_for_z_index()
    
    if not z_index_data:
        print("未找到任何z-index设置")
        return
    
    # 分析冲突
    by_z_index = analyze_z_index_conflicts(z_index_data)
    
    # 建议层级结构
    suggest_z_index_hierarchy()
    
    # 检查修复状态
    print("\n=== 修复状态检查 ===")
    
    # 检查是否还有9999的z-index
    high_z_index = [item for item in z_index_data if item['z_index'] >= 9999]
    if high_z_index:
        print("⚠️  发现高z-index值 (>=9999):")
        for item in high_z_index:
            print(f"  - {item['file']}:{item['line']} - z-index: {item['z_index']}")
    else:
        print("✅ 没有发现过高的z-index值")
    
    # 检查模态框z-index
    modal_items = [item for item in z_index_data if 'modal' in item['context'].lower()]
    if modal_items:
        print(f"\n模态框z-index设置:")
        for item in modal_items:
            print(f"  - {item['file']}:{item['line']} - z-index: {item['z_index']}")
    
    print(f"\n总共找到 {len(z_index_data)} 个z-index设置")

if __name__ == "__main__":
    main()
