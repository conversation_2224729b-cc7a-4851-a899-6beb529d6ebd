# 数据库字段名称错误修复报告

## 🚨 问题描述

**错误信息**: `1054 (42S22): Unknown column 'appearance_number' in 'field list'`

**错误原因**: 编辑功能中使用了错误的数据库字段名称

**影响功能**: 物料确认编辑页面无法加载

## 🔍 问题分析

### 1. 错误的字段名称
在编辑路由的SQL查询中使用了不存在的字段名：
- ❌ `appearance_number` (不存在)
- ❌ `function_number` (不存在)
- ❌ `check_item` (不存在)

### 2. 正确的数据库表结构
根据数据库创建脚本，实际的表结构为：

#### material_sample_appearance 表
```sql
CREATE TABLE material_sample_appearance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT,
    check_number INT,           -- ✅ 正确字段名
    check_result TEXT,          -- ✅ 正确字段名
    note TEXT,                  -- ✅ 正确字段名
    other_info TEXT,            -- ✅ 正确字段名
    FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
)
```

#### material_sample_function 表
```sql
CREATE TABLE material_sample_function (
    id INT AUTO_INCREMENT PRIMARY KEY,
    form_id INT,
    check_number INT,           -- ✅ 正确字段名
    check_result TEXT,          -- ✅ 正确字段名
    note TEXT,                  -- ✅ 正确字段名
    burnin_info TEXT,           -- ✅ 附加字段
    electrical_info TEXT,       -- ✅ 附加字段
    tests_info TEXT,            -- ✅ 附加字段
    other_test TEXT,            -- ✅ 附加字段
    other_info TEXT,            -- ✅ 附加字段
    FOREIGN KEY (form_id) REFERENCES material_sample_confirmation_form(id)
)
```

## ✅ 修复内容

### 1. 修复编辑路由查询 (form_handler.py)

#### 外观数据查询修复
```python
# 修复前 (错误)
cursor.execute("""
    SELECT 
        appearance_number,           -- ❌ 错误字段名
        COALESCE(check_item, '') as check_item,  -- ❌ 错误字段名
        COALESCE(NULLIF(check_result, ''), '/') as check_result,
        COALESCE(note, '') as note
    FROM material_sample_appearance
    WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
    ORDER BY appearance_number      -- ❌ 错误字段名
""", (report_code,))

# 修复后 (正确)
cursor.execute("""
    SELECT 
        check_number,               -- ✅ 正确字段名
        COALESCE(NULLIF(check_result, ''), '/') as check_result,
        COALESCE(note, '') as note,
        COALESCE(other_info, '') as other_info  -- ✅ 添加缺失字段
    FROM material_sample_appearance
    WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
    ORDER BY check_number          -- ✅ 正确字段名
""", (report_code,))
```

#### 功能数据查询修复
```python
# 修复前 (错误)
cursor.execute("""
    SELECT 
        function_number,            -- ❌ 错误字段名
        COALESCE(check_item, '') as check_item,  -- ❌ 错误字段名
        COALESCE(NULLIF(check_result, ''), '/') as check_result,
        COALESCE(note, '') as note
    FROM material_sample_function
    WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
    ORDER BY function_number       -- ❌ 错误字段名
""", (report_code,))

# 修复后 (正确)
cursor.execute("""
    SELECT 
        check_number,              -- ✅ 正确字段名
        COALESCE(NULLIF(check_result, ''), '/') as check_result,
        COALESCE(note, '') as note,
        COALESCE(burnin_info, '') as burnin_info,      -- ✅ 添加功能检查字段
        COALESCE(electrical_info, '') as electrical_info,
        COALESCE(tests_info, '') as tests_info,
        COALESCE(other_test, '') as other_test,
        COALESCE(other_info, '') as other_info
    FROM material_sample_function
    WHERE form_id = (SELECT id FROM material_sample_confirmation_form WHERE report_code = %s)
    ORDER BY check_number          -- ✅ 正确字段名
""", (report_code,))
```

### 2. 修复更新路由 (form_handler.py)

#### 外观数据更新修复
```python
# 修复前
INSERT INTO material_sample_appearance 
(form_id, appearance_number, check_item, check_result, note)  -- ❌ 错误字段名
VALUES (%s, %s, %s, %s, %s)

# 修复后
INSERT INTO material_sample_appearance 
(form_id, check_number, check_result, note, other_info)      -- ✅ 正确字段名
VALUES (%s, %s, %s, %s, %s)
```

#### 功能数据更新修复
```python
# 修复前
INSERT INTO material_sample_function 
(form_id, function_number, check_item, check_result, note)   -- ❌ 错误字段名
VALUES (%s, %s, %s, %s, %s)

# 修复后
INSERT INTO material_sample_function 
(form_id, check_number, check_result, note, burnin_info, electrical_info, tests_info, other_test, other_info)  -- ✅ 正确字段名和完整字段
VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
```

### 3. 修复前端数据预填充 (Material_Sample_Confirmation_Form.html)

#### 外观数据预填充修复
```javascript
// 修复前
appearanceData.forEach((item, index) => {
    const rowIndex = index + 1;  // ❌ 使用索引可能不准确
    // ...
    inputs[0].value = item.check_item || '';  // ❌ 错误字段名
});

// 修复后
appearanceData.forEach((item, index) => {
    const checkNumber = item.check_number;    // ✅ 使用正确字段名
    if (checkNumber >= 1 && checkNumber <= 5) {
        const row = document.querySelector(`#appearance-table tbody tr:nth-child(${checkNumber})`);
        // 根据实际表单结构填充数据
    }
});
```

#### 功能数据预填充修复
```javascript
// 修复前
functionData.forEach((item, index) => {
    const rowIndex = index + 1;   // ❌ 使用索引可能不准确
    // ...
    inputs[0].value = item.check_item || '';  // ❌ 错误字段名
});

// 修复后
functionData.forEach((item, index) => {
    const checkNumber = item.check_number;    // ✅ 使用正确字段名
    if (checkNumber >= 1 && checkNumber <= 5) {
        // 根据check_number精确定位到对应的检查项
        // 处理功能检查1-4和功能检查5的不同结构
    }
});
```

## 🔧 修复的文件

### 后端文件
- `blueprints/material_confirmation/form_handler.py`
  - 修复编辑路由的SQL查询字段名
  - 修复更新路由的SQL插入字段名
  - 添加缺失的数据库字段

### 前端文件
- `blueprints/material_confirmation/templates/material_confirmation/Material_Sample_Confirmation_Form.html`
  - 修复JavaScript数据预填充逻辑
  - 使用正确的字段名进行数据映射
  - 改进数据定位方式

## 🧪 测试验证

### 测试步骤
1. 访问物料确认页面: `http://************:5000/material_confirmation/`
2. 选择一条已有记录
3. 点击"修改"按钮
4. 验证编辑页面是否正常加载
5. 检查数据是否正确预填充

### 预期结果
- ✅ 编辑页面正常加载，不再出现数据库字段错误
- ✅ 基本信息正确预填充
- ✅ 尺寸数据正确预填充
- ✅ 外观检查数据正确预填充
- ✅ 功能检查数据正确预填充
- ✅ 问题点数据正确预填充

## 📋 数据库字段对照表

| 功能模块 | 错误字段名 | 正确字段名 | 说明 |
|---------|-----------|-----------|------|
| 外观检查 | appearance_number | check_number | 检查项编号 |
| 外观检查 | check_item | (不存在) | 检查项目内容在表单中直接显示 |
| 功能检查 | function_number | check_number | 检查项编号 |
| 功能检查 | check_item | (不存在) | 检查项目内容在表单中直接显示 |

## 🔄 后续注意事项

### 1. 数据库设计一致性
- 确保所有相关代码使用统一的字段名称
- 建议创建数据库字段映射文档
- 在开发新功能时参考现有表结构

### 2. 代码审查
- 在修改数据库查询时，先确认表结构
- 使用 `DESCRIBE table_name` 命令查看字段定义
- 测试所有相关的CRUD操作

### 3. 错误处理
- 添加更详细的数据库错误日志
- 在开发环境中启用SQL查询日志
- 实现字段验证和错误提示

---

**修复状态**: ✅ 已完成  
**测试状态**: 🧪 待验证  
**更新时间**: 2025年8月2日
