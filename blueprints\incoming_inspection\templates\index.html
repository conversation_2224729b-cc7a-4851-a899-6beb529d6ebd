{% extends "base.html" %}

{% block title %}物料检验 - 品质中心管理系统{% endblock %}

{% block content %}
<div class="page-header">
    <h1>物料检验管理</h1>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-random"></i> 抽样检验</h4>
            </div>
            <div class="card-body">
                <p>对物料进行抽样检验，根据抽检结果判断整批物料质量</p>
                <div class="btn-group">
                    <a href="{{ url_for('sampling_inspection.index') }}" class="btn btn-primary"><i class="fas fa-list"></i> 检验记录</a>
                    <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-success"><i class="fas fa-plus"></i> 新增检验</a>
                </div>
            </div>
        </div>
    </div>


</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-chart-bar"></i> 最近检验数据</h4>
            </div>
            <div class="card-body">
                <div id="recent-inspections">
                    <!-- 这里将通过AJAX加载最近的检验数据 -->
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 加载最近检验数据
        fetch('/api/recent_inspections?limit=5')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const recentInspections = document.getElementById('recent-inspections');
                    
                    if (data.records && data.records.length > 0) {
                        // 创建表格
                        const table = document.createElement('table');
                        table.className = 'table table-striped table-sm';
                        
                        // 创建表头
                        const thead = document.createElement('thead');
                        thead.innerHTML = `
                            <tr>
                                <th>检验类型</th>
                                <th>料号</th>
                                <th>物料名称</th>
                                <th>供应商</th>
                                <th>检验日期</th>
                                <th>数量</th>
                                <th>操作</th>
                            </tr>
                        `;
                        table.appendChild(thead);
                        
                        // 创建表体
                        const tbody = document.createElement('tbody');
                        data.records.forEach(record => {
                            const tr = document.createElement('tr');
                            
                            // 检验类型
                            const typeTd = document.createElement('td');
                            typeTd.textContent = record.type === 'full' ? '全检' : '抽检';
                            tr.appendChild(typeTd);
                            
                            // 料号
                            const materialNumberTd = document.createElement('td');
                            materialNumberTd.textContent = record.material_number;
                            tr.appendChild(materialNumberTd);
                            
                            // 物料名称
                            const materialNameTd = document.createElement('td');
                            materialNameTd.textContent = record.material_name;
                            tr.appendChild(materialNameTd);
                            
                            // 供应商
                            const supplierTd = document.createElement('td');
                            supplierTd.textContent = record.supplier;
                            tr.appendChild(supplierTd);
                            
                            // 检验日期
                            const dateTd = document.createElement('td');
                            dateTd.textContent = record.inspection_date.substring(0, 10);
                            tr.appendChild(dateTd);
                            
                            // 数量
                            const quantityTd = document.createElement('td');
                            quantityTd.textContent = `${record.total_quantity} / ${record.qualified_quantity}`;
                            tr.appendChild(quantityTd);
                            
                            // 操作
                            const actionTd = document.createElement('td');
                            const viewLink = document.createElement('a');
                            viewLink.href = `/detail_inspection?id=${record.id}&type=${record.type}`;
                            viewLink.className = 'btn btn-sm btn-info';
                            viewLink.textContent = '详情';
                            actionTd.appendChild(viewLink);
                            tr.appendChild(actionTd);
                            
                            tbody.appendChild(tr);
                        });
                        table.appendChild(tbody);
                        
                        // 清空加载中的内容，添加表格
                        recentInspections.innerHTML = '';
                        recentInspections.appendChild(table);
                    } else {
                        recentInspections.innerHTML = '<p class="text-center">暂无检验记录</p>';
                    }
                } else {
                    console.error('加载检验数据失败:', data.error);
                    document.getElementById('recent-inspections').innerHTML = 
                        '<p class="text-center text-danger">加载数据失败，请刷新页面重试</p>';
                }
            })
            .catch(error => {
                console.error('请求错误:', error);
                document.getElementById('recent-inspections').innerHTML = 
                    '<p class="text-center text-danger">加载数据失败，请刷新页面重试</p>';
            });
    });
</script>
{% endblock %} 