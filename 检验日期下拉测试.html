<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检验日期下拉测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h2 {
            color: #1976d2;
            margin-top: 0;
        }
        
        /* 模拟原始样式 */
        .date-type-dropdown {
            margin-right: 5px;
            font-size: 11px;
            color: #333;
            display: inline-flex;
            align-items: center;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 100 !important;
        }
        
        .date-type-dropdown select {
            height: 32px;
            padding: 2px 8px;
            font-size: 12px;
            border: 1px solid #1976d2;
            border-radius: 4px;
            cursor: pointer;
            background-color: #fff;
            color: #1976d2;
            outline: none;
            width: 100px;
            box-sizing: border-box;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 100 !important;
        }
        
        .date-type-dropdown select:hover {
            background-color: #f8f9fa;
        }
        
        .date-type-dropdown select:focus {
            border-color: #0d47a1;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .problem-description {
            background-color: #ffebee;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .problem-description h3 {
            color: #c62828;
            margin-top: 0;
        }
        
        .solution-description {
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .solution-description h3 {
            color: #2e7d32;
            margin-top: 0;
        }
        
        .log-output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
        
        .test-button {
            background-color: #1976d2;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        
        .test-button:hover {
            background-color: #1565c0;
        }
        
        /* 测试遮挡元素 */
        .overlay-test {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 0, 0, 0.1);
            z-index: 50;
            pointer-events: none;
        }
        
        .blocking-element {
            position: absolute;
            top: 0;
            left: 0;
            width: 200px;
            height: 50px;
            background-color: rgba(0, 0, 255, 0.2);
            z-index: 150;
            pointer-events: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>检验日期下拉无反应问题测试</h2>
            <p>此页面用于测试和诊断检验日期下拉选择器无反应的问题。</p>
        </div>
        
        <div class="problem-description">
            <h3>问题描述</h3>
            <ul>
                <li>点击检验日期下拉框无反应</li>
                <li>可能是CSS样式问题</li>
                <li>可能是JavaScript事件绑定问题</li>
                <li>可能有元素遮挡</li>
            </ul>
        </div>
        
        <div class="solution-description">
            <h3>修复方案</h3>
            <ul>
                <li>添加强制的pointer-events: auto样式</li>
                <li>设置正确的z-index层级</li>
                <li>改进JavaScript事件绑定</li>
                <li>添加调试日志</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>测试1：基本下拉选择器</h3>
            <div class="header-right">
                <span class="date-type-dropdown">
                    <select id="test-selector-1">
                        <option value="inspection_date" selected>检验日期</option>
                        <option value="receipt_date">来料日期</option>
                    </select>
                </span>
                <span>基本测试（无遮挡）</span>
            </div>
        </div>
        
        <div class="test-section" style="position: relative;">
            <h3>测试2：有遮挡元素的下拉选择器</h3>
            <div class="overlay-test"></div>
            <div class="header-right">
                <span class="date-type-dropdown">
                    <select id="test-selector-2">
                        <option value="inspection_date" selected>检验日期</option>
                        <option value="receipt_date">来料日期</option>
                    </select>
                </span>
                <span>有透明遮挡层（z-index: 50）</span>
            </div>
        </div>
        
        <div class="test-section" style="position: relative;">
            <h3>测试3：有高z-index遮挡元素</h3>
            <div class="blocking-element"></div>
            <div class="header-right">
                <span class="date-type-dropdown">
                    <select id="test-selector-3">
                        <option value="inspection_date" selected>检验日期</option>
                        <option value="receipt_date">来料日期</option>
                    </select>
                </span>
                <span>有高z-index遮挡元素（z-index: 150）</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="testAllSelectors()">测试所有选择器</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <button class="test-button" onclick="checkStyles()">检查样式</button>
        </div>
        
        <div class="test-section">
            <h3>事件日志</h3>
            <div id="log-output" class="log-output"></div>
        </div>
    </div>

    <script>
        let logOutput = document.getElementById('log-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logOutput.innerHTML = '';
        }
        
        function testAllSelectors() {
            log('开始测试所有选择器...');
            
            for (let i = 1; i <= 3; i++) {
                const selector = document.getElementById(`test-selector-${i}`);
                if (selector) {
                    log(`测试选择器 ${i}:`);
                    log(`  - 元素存在: ${!!selector}`);
                    log(`  - 当前值: ${selector.value}`);
                    log(`  - 样式 pointer-events: ${getComputedStyle(selector).pointerEvents}`);
                    log(`  - 样式 z-index: ${getComputedStyle(selector).zIndex}`);
                    log(`  - 样式 position: ${getComputedStyle(selector).position}`);
                    
                    // 尝试程序化触发change事件
                    selector.value = selector.value === 'inspection_date' ? 'receipt_date' : 'inspection_date';
                    selector.dispatchEvent(new Event('change'));
                    log(`  - 程序化切换到: ${selector.value}`);
                } else {
                    log(`选择器 ${i} 未找到！`);
                }
            }
        }
        
        function checkStyles() {
            log('检查页面样式...');
            
            // 检查是否有全局样式影响
            const allSelects = document.querySelectorAll('select');
            allSelects.forEach((select, index) => {
                const styles = getComputedStyle(select);
                log(`选择器 ${index + 1} 计算样式:`);
                log(`  - pointer-events: ${styles.pointerEvents}`);
                log(`  - z-index: ${styles.zIndex}`);
                log(`  - position: ${styles.position}`);
                log(`  - display: ${styles.display}`);
                log(`  - visibility: ${styles.visibility}`);
            });
        }
        
        // 初始化事件绑定
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始绑定事件...');
            
            for (let i = 1; i <= 3; i++) {
                const selector = document.getElementById(`test-selector-${i}`);
                if (selector) {
                    // 绑定change事件
                    selector.addEventListener('change', function() {
                        log(`选择器 ${i} 值变化: ${this.value}`);
                    });
                    
                    // 绑定click事件
                    selector.addEventListener('click', function() {
                        log(`选择器 ${i} 被点击`);
                    });
                    
                    // 绑定focus事件
                    selector.addEventListener('focus', function() {
                        log(`选择器 ${i} 获得焦点`);
                    });
                    
                    // 绑定mousedown事件
                    selector.addEventListener('mousedown', function() {
                        log(`选择器 ${i} 鼠标按下`);
                    });
                    
                    log(`选择器 ${i} 事件绑定完成`);
                } else {
                    log(`选择器 ${i} 未找到，跳过事件绑定`);
                }
            }
            
            log('所有事件绑定完成');
        });
        
        // 全局点击事件监听
        document.addEventListener('click', function(event) {
            if (event.target.tagName === 'SELECT') {
                log(`全局监听: SELECT元素被点击 - ${event.target.id || '无ID'}`);
            }
        });
        
        // 页面加载后自动检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('自动检查页面状态...');
                checkStyles();
            }, 1000);
        });
    </script>
</body>
</html>
