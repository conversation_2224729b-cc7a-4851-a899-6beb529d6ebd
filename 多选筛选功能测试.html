<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选筛选功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h2 {
            color: #1976d2;
            margin-top: 0;
        }
        
        /* 筛选区域样式 */
        .filter-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 1px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
            margin-bottom: 10px;
            justify-content: flex-start;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: auto;
            flex: none;
        }
        
        .filter-control {
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            outline: none;
        }
        
        /* 多选下拉框样式 */
        .multi-select-dropdown {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .multi-select-button {
            width: 100%;
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            text-align: left;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .multi-select-button:hover {
            border-color: #1976d2;
        }
        
        .multi-select-button:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
            outline: none;
        }
        
        .multi-select-arrow {
            font-size: 10px;
            color: #6c757d;
            transition: transform 0.2s;
        }
        
        .multi-select-dropdown.open .multi-select-arrow {
            transform: rotate(180deg);
        }
        
        .multi-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ced4da;
            border-top: none;
            border-radius: 0 0 4px 4px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .multi-select-dropdown.open .multi-select-options {
            display: block;
        }
        
        .multi-select-option {
            padding: 8px 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            border-bottom: 1px solid #f1f1f1;
        }
        
        .multi-select-option:last-child {
            border-bottom: none;
        }
        
        .multi-select-option:hover {
            background-color: #f8f9fa;
        }
        
        .multi-select-option input[type="checkbox"] {
            margin: 0;
            width: 14px;
            height: 14px;
        }
        
        .multi-select-option label {
            margin: 0;
            cursor: pointer;
            flex: 1;
            font-size: 12px;
            color: #495057;
        }
        
        .multi-select-text {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .multi-select-count {
            font-size: 10px;
            color: #6c757d;
            background-color: #e9ecef;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 4px;
        }
        
        .filter-actions {
            display: flex;
            gap: 6px;
            align-items: flex-end;
            margin-left: auto;
        }
        
        .filter-btn {
            height: 32px;
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            white-space: nowrap;
        }
        
        .filter-btn-primary {
            background-color: #1976d2;
            color: white;
        }
        
        .filter-btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .log-output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin-top: 20px;
        }
        
        .feature-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .feature-list h3 {
            color: #333;
            margin-top: 0;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>多选筛选功能测试</h2>
            <p>此页面用于测试新增的多选复选框筛选功能，用户可以同时选择多个选项进行筛选。</p>
        </div>
        
        <div class="feature-list">
            <h3>多选功能特性</h3>
            <ul>
                <li><strong>多选支持</strong>：每个下拉框都支持选择多个选项</li>
                <li><strong>复选框交互</strong>：点击复选框选择/取消选择选项</li>
                <li><strong>智能显示</strong>：按钮文本根据选择数量智能显示</li>
                <li><strong>计数显示</strong>：选择多个时显示选择数量</li>
                <li><strong>组合筛选</strong>：多个字段的多选值采用"与"关联</li>
                <li><strong>同字段OR</strong>：同一字段内的多个值采用"或"关联</li>
                <li><strong>状态保持</strong>：筛选后保持选择状态</li>
            </ul>
        </div>
        
        <!-- 多选筛选区域演示 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-group">
                    <input type="text" class="filter-control" placeholder="报告编号">
                </div>
                
                <div class="filter-group">
                    <div class="multi-select-dropdown" id="supplier-dropdown">
                        <button type="button" class="multi-select-button" tabindex="0">
                            <span class="multi-select-text">供应商</span>
                            <span class="multi-select-arrow">▼</span>
                        </button>
                        <div class="multi-select-options">
                            <div class="multi-select-option">
                                <input type="checkbox" id="supplier-1" value="深圳市科技有限公司">
                                <label for="supplier-1">深圳市科技有限公司</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="supplier-2" value="广州电子制造厂">
                                <label for="supplier-2">广州电子制造厂</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="supplier-3" value="东莞精密器件厂">
                                <label for="supplier-3">东莞精密器件厂</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="supplier-4" value="苏州半导体公司">
                                <label for="supplier-4">苏州半导体公司</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-group">
                    <div class="multi-select-dropdown" id="inspection-type-dropdown">
                        <button type="button" class="multi-select-button" tabindex="0">
                            <span class="multi-select-text">检验类型</span>
                            <span class="multi-select-arrow">▼</span>
                        </button>
                        <div class="multi-select-options">
                            <div class="multi-select-option">
                                <input type="checkbox" id="type-1" value="sampling">
                                <label for="type-1">抽样检验</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="type-2" value="full">
                                <label for="type-2">全部检验</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="type-3" value="exempt">
                                <label for="type-3">免检</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-group">
                    <div class="multi-select-dropdown" id="inspection-result-dropdown">
                        <button type="button" class="multi-select-button" tabindex="0">
                            <span class="multi-select-text">检验结果</span>
                            <span class="multi-select-arrow">▼</span>
                        </button>
                        <div class="multi-select-options">
                            <div class="multi-select-option">
                                <input type="checkbox" id="result-1" value="合格">
                                <label for="result-1">合格</label>
                            </div>
                            <div class="multi-select-option">
                                <input type="checkbox" id="result-2" value="不合格">
                                <label for="result-2">不合格</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="filter-actions">
                    <button type="button" class="filter-btn filter-btn-primary" id="apply-filters">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <button type="button" class="filter-btn filter-btn-secondary" id="reset-filters">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        
        <div class="feature-list">
            <h3>使用说明</h3>
            <ol>
                <li><strong>点击下拉按钮</strong>：展开选项列表</li>
                <li><strong>选择多个选项</strong>：点击复选框选择多个选项</li>
                <li><strong>查看选择状态</strong>：按钮文本会显示选择的内容或数量</li>
                <li><strong>点击其他地方</strong>：关闭下拉框</li>
                <li><strong>应用筛选</strong>：点击筛选按钮应用选择的条件</li>
                <li><strong>重置选择</strong>：点击重置按钮清空所有选择</li>
            </ol>
        </div>
        
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        let logOutput = document.getElementById('log-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        // 多选下拉框功能实现
        function initializeMultiSelectDropdowns() {
            log('初始化多选下拉框');
            
            const dropdowns = document.querySelectorAll('.multi-select-dropdown');
            
            dropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('.multi-select-button');
                const options = dropdown.querySelector('.multi-select-options');
                const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
                
                // 初始化显示文本
                updateButtonText(dropdown);
                
                // 点击按钮切换下拉框
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    closeAllDropdowns();
                    dropdown.classList.toggle('open');
                    log(`${dropdown.id} 下拉框${dropdown.classList.contains('open') ? '打开' : '关闭'}`);
                });
                
                // 复选框变化事件
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateButtonText(dropdown);
                        log(`${dropdown.id} 选项变化: ${this.value} ${this.checked ? '选中' : '取消'}`);
                    });
                });
                
                // 阻止选项区域点击时关闭下拉框
                options.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
            
            // 点击其他地方关闭所有下拉框
            document.addEventListener('click', function() {
                closeAllDropdowns();
            });
            
            log('多选下拉框初始化完成');
        }
        
        function closeAllDropdowns() {
            document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                dropdown.classList.remove('open');
            });
        }
        
        function updateButtonText(dropdown) {
            const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
            const textSpan = dropdown.querySelector('.multi-select-text');
            const defaultText = dropdown.id.includes('supplier') ? '供应商' : 
                               dropdown.id.includes('inspection-type') ? '检验类型' : '检验结果';
            
            if (checkboxes.length === 0) {
                textSpan.innerHTML = defaultText;
            } else if (checkboxes.length === 1) {
                textSpan.innerHTML = checkboxes[0].nextElementSibling.textContent;
            } else {
                textSpan.innerHTML = `${defaultText} <span class="multi-select-count">${checkboxes.length}</span>`;
            }
        }
        
        // 获取多选下拉框的选中值
        function getMultiSelectValues(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return [];
            
            const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }
        
        // 应用筛选（演示）
        function applyFilters() {
            log('=== 应用筛选 ===');
            
            const supplierValues = getMultiSelectValues('supplier-dropdown');
            const typeValues = getMultiSelectValues('inspection-type-dropdown');
            const resultValues = getMultiSelectValues('inspection-result-dropdown');
            
            log(`选择的供应商: ${supplierValues.length > 0 ? supplierValues.join(', ') : '无'}`);
            log(`选择的检验类型: ${typeValues.length > 0 ? typeValues.join(', ') : '无'}`);
            log(`选择的检验结果: ${resultValues.length > 0 ? resultValues.join(', ') : '无'}`);
            
            // 构建URL参数
            const params = new URLSearchParams();
            if (supplierValues.length > 0) params.set('supplier', supplierValues.join(','));
            if (typeValues.length > 0) params.set('inspection_type', typeValues.join(','));
            if (resultValues.length > 0) params.set('inspection_result', resultValues.join(','));
            
            const queryString = params.toString();
            log(`生成的查询参数: ${queryString || '(无)'}`);
            
            if (queryString) {
                log(`完整URL: /sampling_inspection/?${queryString}`);
            }
        }
        
        // 重置筛选（演示）
        function resetFilters() {
            log('=== 重置筛选 ===');
            
            document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateButtonText(dropdown);
            });
            
            log('所有筛选条件已重置');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...');
            
            // 初始化多选下拉框
            initializeMultiSelectDropdowns();
            
            // 绑定按钮事件
            document.getElementById('apply-filters').addEventListener('click', applyFilters);
            document.getElementById('reset-filters').addEventListener('click', resetFilters);
            
            log('所有功能初始化完成');
        });
    </script>
</body>
</html>
