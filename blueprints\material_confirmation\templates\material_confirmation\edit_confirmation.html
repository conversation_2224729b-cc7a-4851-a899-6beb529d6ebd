{% extends "base.html" %}

{% block title %}编辑物料确认 - 品质中心管理系统{% endblock %}

{% block content %}
<div class="page-header">
    <h1>编辑物料确认 - {{ confirmation.report_code }}</h1>
</div>

<div class="card">
    <div class="card-body">
        <div class="alert alert-info">
            <h4>功能开发中</h4>
            <p>编辑物料确认功能正在开发中，敬请期待。</p>
            <p>如需编辑完整的物料确认信息，请访问检验系统。</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h5>基本信息</h5>
                <table class="table table-bordered">
                    <tr><td>报告编码</td><td>{{ confirmation.report_code }}</td></tr>
                    <tr><td>供应商</td><td>{{ confirmation.supplier }}</td></tr>
                    <tr><td>物料料号</td><td>{{ confirmation.material_number }}</td></tr>
                    <tr><td>物料名称</td><td>{{ confirmation.material_name }}</td></tr>
                    <tr><td>检验员</td><td>{{ confirmation.inspector }}</td></tr>
                    <tr><td>最终判定</td><td>{{ confirmation.final_judgment }}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h5>问题点</h5>
                {% if confirmation.questions %}
                <ul class="list-group">
                    {% for question in confirmation.questions %}
                    <li class="list-group-item">
                        <strong>{{ question.question_number }}.</strong> {{ question.question_text }}
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <p class="text-muted">无问题点记录</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-3">
            <a href="{{ url_for('material_confirmation.index') }}" class="btn btn-secondary">返回确认清单</a>
            <a href="{{ url_for('material_confirmation.view_confirmation', report_code=confirmation.report_code) }}" class="btn btn-info">查看详情</a>
        </div>
    </div>
</div>
{% endblock %}
