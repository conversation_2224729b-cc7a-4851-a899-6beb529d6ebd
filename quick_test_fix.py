#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复结果
"""

def test_load_data_handler_variables():
    """测试load_data_handler.py是否包含所有必要的变量"""
    print("检查load_data_handler.py中的变量传递...")
    
    try:
        with open('blueprints/material_confirmation/load_data_handler.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        required_vars = [
            'size_row_count=size_row_count',
            'size_1_position=',
            'size_1_value=',
            'size_1_check=',
            'function_1_check=',
            'function_1_note=',
            'function_5_tests=',
            'function_6_other='
        ]
        
        missing_vars = []
        for var in required_vars:
            if var not in content:
                missing_vars.append(var)
            else:
                print(f"✅ {var} 存在")
                
        if missing_vars:
            print("❌ 缺少以下变量:")
            for var in missing_vars:
                print(f"   - {var}")
            return False
        else:
            print("✅ 所有必要变量都已添加")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_template_variables():
    """检查模板中使用的关键变量"""
    print("\n检查模板中的关键变量使用...")
    
    try:
        with open('blueprints/material_confirmation/templates/material_confirmation/Material_Sample_Confirmation_Form_load_data.html', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查一些关键变量
        key_vars = [
            '{{ size_row_count',
            '{{ size_1_position }}',
            '{{ function_1_check }}',
            '{{ function_5_tests',
            '{{ appearance_data[0]'
        ]
        
        for var in key_vars:
            if var in content:
                print(f"✅ 模板使用了 {var}")
            else:
                print(f"❌ 模板未使用 {var}")
                
        return True
        
    except Exception as e:
        print(f"❌ 模板检查失败: {e}")
        return False

def main():
    print("=" * 60)
    print("快速测试load-data修复结果")
    print("=" * 60)
    
    test1 = test_load_data_handler_variables()
    test2 = test_template_variables()
    
    print("\n" + "=" * 60)
    if test1 and test2:
        print("🎉 修复检查通过！")
        print("\n📋 修复内容:")
        print("1. ✅ 添加了size_row_count变量计算")
        print("2. ✅ 添加了所有尺寸字段的单独变量")
        print("3. ✅ 添加了所有功能检查字段的单独变量")
        print("4. ✅ 模板变量使用正确")
        print("\n🚀 现在应该可以正常访问load-data页面了！")
        print("请重启Flask应用后测试：")
        print("1. 停止当前Flask应用（如果正在运行）")
        print("2. 运行: python app.py")
        print("3. 访问: http://127.0.0.1:5000/material_confirmation/")
        print("4. 勾选记录并点击'查看报告详情'")
    else:
        print("⚠️  修复检查未完全通过，请检查相关问题")
    print("=" * 60)

if __name__ == "__main__":
    main()
