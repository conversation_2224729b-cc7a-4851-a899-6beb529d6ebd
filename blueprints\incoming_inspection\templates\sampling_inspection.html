{% extends "base.html" %}

{% block title %}抽样检验 - 品质中心管理系统{% endblock %}

{# 分页链接宏 #}
{% macro pagination_url(target_page) -%}
{{ url_for('sampling_inspection.index',
    page=target_page,
    per_page=per_page,
    start_date=start_date,
    end_date=end_date,
    date_type=date_type,
    material_number=material_number,
    supplier=supplier,
    quick_search=quick_search,
    purchase_order=purchase_order,
    inspection_result=inspection_result,
    defect_rate=defect_rate,
    defect_rate_op=defect_rate_op,
    total_quantity=total_quantity,
    total_quantity_op=total_quantity_op,
    report_code=report_code,
    inspection_type=inspection_type,
    receipt_start_date=receipt_start_date,
    receipt_end_date=receipt_end_date
) }}
{%- endmacro %}

{% block extra_css %}
<style>
    /* 确保页面布局不影响导航栏 */
    body {
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
        height: 100vh !important;
        max-height: 100vh !important;
    }

    .container {
        margin: 0 !important;
        padding: 4px !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
        height: calc(100vh - 36px) !important; /* 减去导航栏高度 */
        max-height: calc(100vh - 36px) !important;
        overflow: hidden !important;
        display: flex !important;
        flex-direction: column !important;
    }

    /* 确保页面内容区域正确布局 */
    .page-header {
        flex-shrink: 0 !important; /* 防止头部被压缩 */
    }

    .filter-section {
        flex-shrink: 0 !important; /* 防止筛选区域被压缩 */
    }

    .pagination-container {
        flex-shrink: 0 !important; /* 防止分页区域被压缩 */
    }
    .modal {
        display: none;
        position: fixed;
        z-index: 9998;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
    }
    
    .modal-content {
        background-color: white;
        margin: 7% auto;
        padding: 8px;
        border-radius: 3px;
        width: 80%;
        max-width: 500px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
    
    .close {
        color: #aaa;
        float: right;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .record-details p {
        margin: 2px 0;
        font-size: 11px;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }
    
    .pagination a, .pagination span {
        padding: 3px 6px;
        text-decoration: none;
        border: 1px solid #ddd;
        margin: 0 2px;
        color: #333;
        font-size: 11px;
    }
    
    .pagination a:hover {
        background-color: #f1f1f1;
    }
    
    .pagination .active {
        background-color: #1976d2;
        color: white;
        border: 1px solid #1976d2;
    }
    
    .pagination .disabled {
        color: #aaa;
        border: 1px solid #ddd;
    }
    
    .summary {
        margin-bottom: 5px;
        color: #666;
        font-size: 0.75em;
    }
    
    /* 分页容器样式 */
    .pagination-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        padding: 5px 0;
    }
    
    .page-size-selector {
        font-size: 11px;
        color: #666;
        display: flex;
        align-items: center;
        white-space: nowrap;
        min-width: 130px;
    }
    
    .page-size-selector select {
        padding: 2px 5px;
        border: 1px solid #ddd;
        border-radius: 3px;
        font-size: 11px;
        margin-left: 5px;
        cursor: pointer;
        width: 65px;
    }
    
    .pagination-controls {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
    }
    
    /* 表格紧凑样式 */
    .sortable-table {
        font-size: 11px;
        width: 100%;
        table-layout: auto;
        border-collapse: collapse;
    }
    
    .sortable-table th, .sortable-table td {
        padding: 3px 6px;
        white-space: nowrap;
        border: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;
    }
    
    .sortable-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .sortable-table td[data-column="defect_issues"] {
        white-space: normal;
        max-width: 150px;
        text-align: left;
    }
    
    /* 按钮样式 */
    .btn {
        padding: 2px 5px;
        font-size: 11px;
    }

    /* 检验类型徽章样式 */
    .badge {
        display: inline-block;
        padding: 2px 6px;
        font-size: 10px;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 3px;
    }

    .badge-primary {
        color: #fff;
        background-color: #007bff;
    }

    .badge-warning {
        color: #212529;
        background-color: #ffc107;
    }

    .badge-success {
        color: #fff;
        background-color: #28a745;
    }

    .badge-secondary {
        color: #fff;
        background-color: #6c757d;
    }
    
    /* 表头样式 */
    .sortable-table th {
        background-color: #f5f5f5;
        font-weight: 600;
        font-size: 11px;
    }
    
    /* 表格容器 - 精确自适应浏览器高度 */
    .table-container {
        margin: 0;
        padding: 0;
        width: 100%;
        box-sizing: border-box;
        flex: 1 1 auto !important; /* 使用flex布局自动填充剩余空间 */
        overflow-x: auto;
        overflow-y: auto;
        position: relative;
        min-height: 400px;
    }
    
    /* 紧凑表格行 */
    .sortable-table tr {
        height: 24px;
        transition: background-color 0.2s;
    }

    .sortable-table tbody tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    .sortable-table tbody tr:hover {
        background-color: #eaf2fd;
    }
    
    /* 搜索表单样式 */
    .search-form {
        background-color: #f8f9fa;
        padding: 6px;
        border-radius: 3px;
        margin-bottom: 8px;
        border: 1px solid #e9ecef;
    }
    
    /* 表单元素样式 */
    .form-group {
        margin-bottom: 5px;
        display: flex;
        align-items: center;
    }
    
    .form-label {
        width: 70px;
        text-align: right;
        padding-right: 8px;
        flex-shrink: 0;
        font-size: 11px;
        font-weight: 500;
        color: #555;
    }
    
    .form-input {
        flex: 1;
    }
    
    /* 表单元素样式统一 */
    input[type="text"], input[type="date"], input[type="number"], select, .quick-search-input {
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        width: 100%;
        max-width: 180px;
        box-sizing: border-box;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    /* 日期组样式 */
    .date-group {
        margin-bottom: 10px;
    }
    
    .date-label {
        font-size: 11px;
        font-weight: 500;
        color: #1976d2;
        margin-bottom: 4px;
        border-bottom: 1px solid #e0e0e0;
        padding-bottom: 2px;
    }
    
    .date-inputs {
        display: flex;
        gap: 10px;
    }
    
    .date-inputs .form-group {
        margin-bottom: 0;
    }
    
    .date-inputs .form-label {
        width: 40px;
    }
    
    /* 范围输入样式 */
    .range-input {
        display: flex;
        align-items: center;
        gap: 4px;
    }
    
    .operator-select {
        width: 80px;
        flex-shrink: 0;
    }
    
    .unit-label {
        font-size: 11px;
        color: #555;
        margin-left: 2px;
    }
    
    /* 两列布局 */
    .two-column-layout {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -8px;
    }
    
    .column {
        padding: 0 8px;
        box-sizing: border-box;
    }
    
    .left-column {
        flex: 0 0 40%;
    }
    
    .right-column {
        flex: 0 0 60%;
    }
    
    .search-buttons {
        text-align: right;
        margin-top: 5px;
    }
    
    /* 页面标题和操作按钮区域 */
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .header-left {
        display: flex;
        align-items: center;
    }
    
    .header-right {
        display: flex;
        gap: 8px;
    }
    
    /* 快速搜索表单 */
    .quick-search-form {
        display: flex;
        align-items: center;
        margin-right: 10px;
        position: relative;
        border: 1px solid #1976d2;
        border-radius: 4px;
        overflow: hidden;
        background-color: #fff;
        box-sizing: border-box;
    }
    
    .quick-search-input {
        height: 32px;
        width: 180px;
        padding: 2px 8px;
        padding-right: 30px;
        border: none;
        font-size: 12px;
        outline: none;
        background: transparent;
        z-index: 1;
        max-width: none;
    }
    
    .search-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: #1976d2;
        font-size: 14px;
        cursor: pointer;
        z-index: 2;
        pointer-events: auto;
    }
    
    /* 日期范围选择器 */
    .date-range-picker {
        position: relative;
        margin-right: 10px;
    }
    
    .date-range-display {
        height: 32px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        border: 1px solid #1976d2;
        border-radius: 4px;
        background-color: #fff;
        cursor: pointer;
        font-size: 12px;
        min-width: 200px;
        box-sizing: border-box;
    }
    
    .date-range-display i {
        margin: 0 8px;
        color: #1976d2;
    }
    
    .date-type-dropdown {
        margin-right: 5px;
        font-size: 11px;
        color: #333;
        display: inline-flex;
        align-items: center;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 100 !important;
    }
    
    .date-type-dropdown select {
        height: 32px;
        padding: 2px 8px;
        font-size: 12px;
        border: 1px solid #1976d2;
        border-radius: 4px;
        cursor: pointer;
        background-color: #fff;
        color: #1976d2;
        outline: none;
        width: 100px;
        box-sizing: border-box;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 100 !important;
    }
    
    .date-range-popup {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1500;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 10px;
        width: 480px; /* 减小宽度 */
        margin-top: 5px;
        overflow: visible;
        box-sizing: border-box;
        max-height: none;
    }
    
    .date-popup-layout {
        display: flex;
        gap: 8px; /* 进一步减小间距 */
    }

    .quick-ranges-column {
        width: 70px; /* 进一步减小宽度 */
        border-right: 1px solid #eee;
        padding-right: 8px;
        padding-top: 4px; /* 添加顶部内边距 */
    }

    .calendars-column {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .calendar-container {
        display: flex;
        gap: 8px; /* 减小间距 */
        justify-content: space-between;
    }

    .quick-range-btn {
        font-size: 11px;
        padding: 3px 0; /* 减小内边距 */
        margin-bottom: 4px; /* 减小间距 */
        background-color: transparent;
        border: none;
        border-radius: 0;
        cursor: pointer;
        text-align: left;
        transition: color 0.2s;
        color: #666;
        display: block; /* 确保是块级元素 */
        width: 100%; /* 占满整个容器宽度 */
    }

    .quick-range-btn:hover {
        background-color: transparent; /* 移除悬停背景 */
        color: #1976d2; /* 悬停时改变颜色 */
    }

    .date-range-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px; /* 减小间距 */
        gap: 8px; /* 减小间距 */
        padding-top: 8px; /* 减小内边距 */
        border-top: 1px solid #f0f0f0;
    }
    
    .date-range-actions button {
        padding: 4px 12px; /* 减小按钮内边距 */
        font-size: 12px;
        min-width: 60px; /* 减小最小宽度 */
        height: 26px; /* 减小高度 */
        line-height: 1;
    }
    
    .date-range-actions button.btn-primary {
        background-color: #1976d2;
        border-color: #1976d2;
    }
    
    .date-range-actions button.btn-primary:hover {
        background-color: #1565c0;
        border-color: #1565c0;
    }
    
    #quick-search-btn {
        height: 28px;
        padding: 0 10px;
        border: none;
        background-color: #1976d2;
        color: white;
        font-size: 12px;
        cursor: pointer;
        border-radius: 0;
    }
    
    #quick-search-btn:hover {
        background-color: #1565c0;
    }
    
    /* 高级搜索模态框 */
    #advanced-search-modal .modal-content {
        max-width: 600px;
    }

    /* 确保模态框中的输入框可以正常交互 */
    #advanced-search-modal input,
    #advanced-search-modal select,
    #advanced-search-modal textarea {
        pointer-events: auto !important;
        z-index: 10000 !important;
        position: relative !important;
    }

    /* 确保模态框本身有足够高的z-index和交互性 */
    #advanced-search-modal {
        z-index: 10000 !important;
        pointer-events: auto !important;
    }

    /* 当模态框显示时确保可以交互 */
    #advanced-search-modal[style*="display: block"] {
        pointer-events: auto !important;
    }

    /* 确保表单元素可以获得焦点 */
    #advanced-search-modal .form-control {
        pointer-events: auto !important;
        cursor: text !important;
    }

    /* 强制确保日期类型选择器可以交互 */
    #date-type-selector {
        pointer-events: auto !important;
        position: relative !important;
        z-index: 200 !important;
        cursor: pointer !important;
        user-select: auto !important;
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
    }

    /* 确保日期类型选择器容器不阻止事件 */
    .date-type-dropdown {
        pointer-events: auto !important;
        position: relative !important;
        z-index: 200 !important;
    }

    /* 筛选区域样式 */
    .filter-section {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 15px;
        margin: 1px 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        pointer-events: auto !important;
        position: relative !important;
        z-index: 100 !important;
    }

    .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        align-items: center;
        margin-bottom: 10px;
        justify-content: flex-start;
    }

    .filter-row:last-child {
        margin-bottom: 0;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        min-width: auto;
        flex: none;
    }

    .filter-control {
        height: 30px;
        padding: 1px 8px;
        font-size: 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        color: #495057;
        outline: none;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
        cursor: text !important;
        width: auto;
        max-width: 180px;
    }

    .filter-control:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }

    /* 为select元素设置正确的cursor */
    .filter-control select,
    select.filter-control {
        cursor: pointer !important;
    }

    /* 为input元素设置正确的cursor */
    .filter-control input,
    input.filter-control {
        cursor: text !important;
    }

    .filter-control::placeholder {
        color: #6c757d;
        opacity: 1;
    }

    .filter-number-group {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .filter-operator {
        width: 80px;
        height: 30px;
        padding: 1px 6px;
        font-size: 11px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        color: #495057;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
        cursor: pointer !important;
    }

    .filter-number {
        width: 90px;
        height: 30px;
        padding: 1px 8px;
        font-size: 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        color: #495057;
        text-align: center;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
        cursor: text !important;
    }

    .filter-actions {
        display: flex;
        gap: 6px;
        align-items: center;
        margin-left: 0;
    }

    .filter-btn {
        height: 30px;
        padding: 4px 12px;
        font-size: 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.15s ease-in-out;
        white-space: nowrap;
    }

    .filter-btn-primary {
        background-color: #1976d2;
        color: white;
    }

    .filter-btn-primary:hover {
        background-color: #1565c0;
    }

    .filter-btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .filter-btn-secondary:hover {
        background-color: #5a6268;
    }

    /* 强制确保所有筛选控件可以交互 */
    .filter-section input,
    .filter-section select {
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
        user-select: auto !important;
        -webkit-user-select: auto !important;
        -moz-user-select: auto !important;
        -ms-user-select: auto !important;
    }

    .filter-section input:disabled,
    .filter-section select:disabled {
        pointer-events: none !important;
        opacity: 0.6;
    }

    /* 确保筛选区域内的所有元素都可以交互 */
    .filter-section * {
        pointer-events: auto !important;
    }

    /* 但是span等非交互元素除外 */
    .filter-section span:not(.filter-control) {
        pointer-events: none !important;
    }

    /* 多选下拉框样式 */
    .multi-select-dropdown {
        position: relative;
        display: inline-block;
        width: auto;
        min-width: 120px;
        max-width: 180px;
    }

    .multi-select-button {
        width: auto;
        min-width: 120px;
        max-width: 180px;
        height: 30px;
        padding: 1px 8px;
        font-size: 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        color: #495057;
        text-align: left;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 101 !important;
    }

    .multi-select-button:hover {
        border-color: #1976d2;
    }

    .multi-select-button:focus {
        border-color: #1976d2;
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        outline: none;
    }

    .multi-select-arrow {
        font-size: 10px;
        color: #6c757d;
        transition: transform 0.2s;
    }

    .multi-select-dropdown.open .multi-select-arrow {
        transform: rotate(180deg);
    }

    .multi-select-options {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ced4da;
        border-top: none;
        border-radius: 0 0 4px 4px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .multi-select-dropdown.open .multi-select-options {
        display: block;
    }

    .multi-select-option {
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        border-bottom: 1px solid #f1f1f1;
    }

    .multi-select-option:last-child {
        border-bottom: none;
    }

    .multi-select-option:hover {
        background-color: #f8f9fa;
    }

    .multi-select-option input[type="checkbox"] {
        margin: 0;
        width: 14px;
        height: 14px;
    }

    .multi-select-option label {
        margin: 0;
        cursor: pointer;
        flex: 1;
        font-size: 12px;
        color: #495057;
    }

    .multi-select-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .multi-select-count {
        font-size: 10px;
        color: #6c757d;
        background-color: #e9ecef;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 4px;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .two-column-layout {
            flex-direction: column;
        }

        .left-column,
        .right-column {
            flex: 0 0 100%;
        }

        .form-group {
            flex-direction: column;
            align-items: flex-start;
        }

        .form-label {
            width: 100%;
            text-align: left;
            margin-bottom: 2px;
        }

        .page-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .header-left {
            margin-bottom: 8px;
        }

        .header-right {
            width: 100%;
            flex-wrap: wrap;
        }

        .quick-search-form {
            margin-right: 0;
            margin-bottom: 8px;
            width: 100%;
            border: 1px solid #1976d2;
        }

        .quick-search-input {
            flex: 1;
            width: auto;
            padding-right: 30px;
        }

        /* 移动端表格容器高度调整 */
        .table-container {
            flex: 1 1 auto !important; /* 移动端也使用flex布局 */
            min-height: 300px;
            overflow-x: auto;
            overflow-y: auto;
        }
    }

    .sortable-table td:last-child {
        width: 80px;
        text-align: center;
    }

    .sortable-table .btn-primary {
        background-color: #1976d2;
        color: white;
        border: none;
        border-radius: 3px;
        transition: background-color 0.2s;
    }

    .sortable-table .btn-primary:hover {
        background-color: #1565c0;
    }

    .calendar {
        width: 170px; /* 进一步减小宽度 */
        border: 1px solid #ddd;
        border-radius: 3px; /* 减小圆角 */
        padding: 5px; /* 减小内边距 */
        background-color: #fff;
        margin: 0; /* 移除外边距 */
    }
    
    .calendar-label {
        font-size: 12px;
        font-weight: 500;
        color: #1976d2;
        text-align: center;
        margin-bottom: 5px;
        padding-bottom: 3px;
        border-bottom: 1px solid #eee;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px; /* 减小间距 */
        padding-bottom: 3px; /* 减小内边距 */
        border-bottom: 1px solid #eee;
    }

    .calendar-title {
        font-size: 11px; /* 减小字体 */
        font-weight: 500;
        color: #333;
    }

    .calendar-nav {
        cursor: pointer;
        color: #1976d2;
        font-size: 10px; /* 减小字体 */
        padding: 1px 3px; /* 减小内边距 */
    }

    .calendar-weekdays {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px; /* 减小间距 */
        margin-bottom: 3px; /* 减小间距 */
    }

    .weekday {
        text-align: center;
        font-size: 10px; /* 减小字体 */
        font-weight: 500;
        color: #666;
        padding: 2px 0; /* 减小内边距 */
    }

    .calendar-days {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px; /* 减小间距 */
    }

    .day {
        text-align: center;
        font-size: 10px;
        padding: 3px 0; /* 减小内边距 */
        cursor: pointer;
        border-radius: 2px;
        min-width: 20px; /* 设置最小宽度 */
        height: 20px; /* 设置固定高度 */
        line-height: 14px; /* 调整行高 */
        position: relative;
        z-index: 1;
    }

    .day:hover {
        background-color: #e6f2ff;
        z-index: 2;
    }

    .day.empty {
        cursor: default;
    }

    .day.today {
        background-color: #e3f2fd;
        font-weight: bold;
        color: #1976d2;
        border: 1px solid #1976d2;
    }

    .day.selected {
        background-color: #e6f2ff;
    }

    .day.start-date {
        background-color: #1976d2;
        color: white;
        position: relative;
    }

    .day.end-date {
        background-color: #ff5722;
        color: white;
        position: relative;
    }
    
    .day.start-date::after, .day.end-date::after {
        content: "";
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: white;
    }
    
    .day.disabled {
        color: #ccc;
        cursor: not-allowed;
        text-decoration: line-through;
        background-color: #f9f9f9;
    }
    
    .day.disabled:hover {
        background-color: #f9f9f9;
    }

    /* 隐藏原始日期输入框 */
    #start-date-input, #end-date-input {
        display: none;
    }

    /* 移动端响应式样式 */
    @media (max-width: 768px) {
        .date-range-popup {
            width: 95% !important;
            left: 2.5%;
            right: 2.5%;
            padding: 8px;
            position: fixed;
            top: 50%;
            transform: translateY(-50%);
            max-height: 90vh;
            overflow-y: auto;
        }
        
        .date-popup-layout {
            flex-direction: column;
        }
        
        .quick-ranges-column {
            width: 100%;
            border-right: none;
            border-bottom: 1px solid #eee;
            padding-bottom: 12px;
            margin-bottom: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .quick-range-btn {
            padding: 8px 12px;
            margin: 0;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #eee;
            min-width: 80px;
            text-align: center;
            font-size: 13px;
            font-weight: 500;
            color: #1976d2;
        }
        
        .quick-range-btn:active {
            background-color: #e0e0e0;
            transform: scale(0.98);
        }
        
        .calendars-column {
            width: 100%;
        }
        
        .calendar-container {
            flex-direction: column;
            gap: 10px;
        }
        
        .calendar {
            width: 100%;
        }
        
        .day {
            height: 28px;
            min-width: 28px;
            line-height: 22px;
            font-size: 12px;
        }
        
        .date-range-display {
            width: 100%;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
        }
        
        .date-range-display i {
            margin-right: 8px;
            font-size: 16px;
        }
        
        /* 移动端顶部菜单样式优化 */
        @media (max-width: 480px) {
            .header-right > * {
                margin-bottom: 8px;
            }
            
            .page-header {
                margin-bottom: 15px;
            }
            
            .header-left h1 {
                font-size: 18px;
                margin-bottom: 10px;
            }
            
            .quick-search-form {
                width: 100%;
                margin-right: 0;
            }
            
            .quick-search-input {
                width: 100%;
                height: 36px;
            }
            
            #advanced-search-btn, 
            .header-right .btn {
                height: 36px;
                width: 48%;
                margin: 0;
                font-size: 14px;
                padding: 0 10px;
            }
        }
        
        .date-type-dropdown {
            margin-bottom: 5px;
            width: 100%;
        }
        
        .date-type-dropdown select {
            width: 100%;
        }
        
        /* 调整头部按钮布局 */
        .header-right {
            flex-wrap: wrap;
        }
        
        .date-range-picker {
            width: 100%;
            margin-right: 0;
            margin-bottom: 5px;
        }
    }

    /* 高级搜索样式优化 */
    .date-group {
        margin-bottom: 12px;
    }
    
    .date-group + .date-group {
        margin-top: 0;
        margin-bottom: 12px;
    }
    
    .date-label {
        font-size: 13px;
        color: #333;
        font-weight: 500;
        margin-bottom: 5px;
        padding-bottom: 2px;
    }

    /* 日期标签提示样式 */
    .date-label small {
        font-weight: normal;
        font-style: italic;
    }

    /* 日期输入框验证状态 */
    .form-control.date-warning {
        border-color: #ffc107;
        box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
    }

    .form-control.date-error {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    
    .date-inputs {
        display: flex;
        gap: 10px;
    }
    
    .date-inputs .form-group {
        margin-bottom: 0;
    }
    
    .date-inputs .form-label {
        width: 40px;
    }
    
    /* 移动端优化 */
    @media (max-width: 768px) {
        .date-group + .date-group {
            margin-top: 3px;
        }
        
        .date-label {
            margin-bottom: 3px;
        }
        
        .date-inputs {
            gap: 5px;
        }
    }

    /* 按钮样式统一 */
    .btn {
        height: 32px; /* 减小高度 */
        line-height: 30px; /* 减小行高 */
        padding: 0 12px; /* 减小左右内边距 */
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px; /* 减小字体大小 */
        transition: all 0.2s;
        vertical-align: middle;
        box-sizing: border-box;
    }
    
    .btn-secondary, .btn-success, .btn-primary {
        min-width: 80px; /* 减小最小宽度 */
    }
    
    #advanced-search-btn, .header-right .btn-success {
        height: 32px; /* 减小高度 */
        vertical-align: middle;
    }

    /* 取消新增记录按钮的下划线 */
    .btn-success {
        text-decoration: none;
    }

    /* 确保按钮文本垂直居中 */
    .buttons-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    /* 顶部布局优化 */
    .header-right {
        display: flex;
        gap: 8px;
        align-items: center;
    }
    
    .header-left {
        display: flex;
        align-items: center;
    }

    /* 在非移动设备上保持原有布局 */
    .date-filters-container {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    /* 日期范围选择器特定样式 */
    @media (min-width: 769px) {
        /* 网页版布局保持不变 */
        .date-type-dropdown {
            margin-right: 0;
        }
    }
    
    /* 移动端响应式样式 */
    @media (max-width: 768px) {
        /* 其他移动端样式保持不变 */
        
        .quick-search-form {
            width: 100%;
            margin-right: 0;
            margin-bottom: 8px;
            height: 38px; /* 增加高度 */
        }
        
        .quick-search-input {
            width: 100%;
            height: 38px; /* 增加高度 */
            font-size: 13px; /* 增加字体大小 */
        }
        
        .date-range-display {
            width: 100%;
            height: 38px; /* 增加高度与其他元素一致 */
        }
        
        .date-type-dropdown select {
            height: 38px; /* 增加高度与日期选择器一致 */
            font-size: 13px; /* 增加字体大小 */
        }
        
        #advanced-search-btn, 
        .header-right .btn {
            height: 38px; /* 增加高度与其他元素一致 */
            width: 48%;
            margin: 0;
            font-size: 13px; /* 增加字体大小 */
            padding: 0 10px;
        }
        
        /* 移动端特定日期选择器布局 */
        @media (max-width: 600px) {
            /* 日期选择器和类型选择器布局重组 */
            .date-filters-container {
                display: flex;
                width: 100%;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .date-type-dropdown {
                width: 38%;
                margin-bottom: 0;
                margin-right: 2%;
            }
            
            .date-range-picker {
                width: 60%;
                margin-bottom: 0;
            }
            
            /* 搜索表单和按钮布局 */
            .quick-search-form {
                width: 100%;
                margin-bottom: 8px;
            }
        }
    }

    /* 移动端响应式分页样式 */
    @media (max-width: 768px) {
        .pagination-container {
            flex-wrap: nowrap;
            gap: 5px;
        }
        
        .summary {
            order: 1;
            width: auto;
            min-width: 0;
            text-align: left;
            margin-right: 5px;
        }
        
        .pagination {
            order: 2;
            width: auto;
            margin-top: 0;
            flex-grow: 1;
            justify-content: center;
        }
        
        .page-size-selector {
            order: 3;
            width: auto;
            min-width: 0;
            justify-content: flex-end;
        }
        
        .pagination a, .pagination span {
            padding: 3px 6px;
        }
    }

    @media (max-width: 480px) {
        .pagination a, .pagination span {
            padding: 4px 6px;
            min-width: 20px;
            font-size: 12px;
        }

        /* 在最小屏幕上调整内容 */
        .summary {
            font-size: 10px;
            min-width: 0;
        }

        .page-size-selector {
            font-size: 10px;
            min-width: 0;
        }

        .page-size-selector select {
            width: 40px;
            font-size: 10px;
        }

        /* 筛选区域响应式 */
        .filter-row {
            flex-direction: column;
            gap: 6px;
        }

        .filter-group {
            min-width: 100%;
            width: 100%;
        }

        .filter-control,
        .multi-select-dropdown,
        .multi-select-button {
            width: 100% !important;
            max-width: 100% !important;
        }

        .filter-actions {
            margin-left: 0;
            justify-content: center;
            width: 100%;
        }

        .filter-number-group {
            justify-content: space-between;
            width: 100%;
        }

        .filter-operator {
            width: 100px;
        }

        .filter-number {
            flex: 1;
            min-width: 120px;
        }

        /* 小屏幕表格容器高度调整 */
        .table-container {
            flex: 1 1 auto !important; /* 小屏幕也使用flex布局 */
            min-height: 250px;
            overflow-x: auto;
            overflow-y: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="header-left">
    <h1>抽样检验记录</h1>
</div>
    <div class="header-right">
        <!-- 日期筛选区域 -->
        <div class="date-filters-container">
            <span class="date-type-dropdown">
                <select id="date-type-selector">
                    <option value="inspection_date" selected>检验日期</option>
                    <option value="receipt_date">来料日期</option>
                </select>
            </span>
            <div class="date-range-picker">
                <div class="date-range-display" id="date-range-display">
                    <i class="fas fa-calendar-alt"></i>
                    <span id="selected-date-range">2025-06-14 ~ 2025-07-14</span>
                </div>
                <div class="date-range-popup" id="date-range-popup">
                    <div class="date-popup-layout">
                        <div class="quick-ranges-column">
                            <div class="quick-range-btn" data-days="0">今天</div>
                            <div class="quick-range-btn" data-days="1">昨天</div>
                            <div class="quick-range-btn" data-days="7">近7天</div>
                            <div class="quick-range-btn" data-days="30">近30天</div>
                            <div class="quick-range-btn" data-days="60">近60天</div>
                            <div class="quick-range-btn" data-days="90">近90天</div>
                            <div class="quick-range-btn" data-days="180">近6个月</div>
                            <div class="quick-range-btn" data-days="365">今年</div>
                        </div>
                        <div class="calendars-column">
                            <div class="calendar-container">
                                <!-- 日历将由JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="start-date-input">
                    <input type="hidden" id="end-date-input">
                    <div class="date-range-actions">
                        <button type="button" class="btn btn-secondary" id="cancel-date-range">取消</button>
                        <button type="button" class="btn btn-primary" id="confirm-date-range">确认</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="quick-search-form">
            <input type="text" id="quick-search-input" class="quick-search-input" placeholder="请输入料号/名称/供应商/采购单号/检验结果进行搜索...">
            <i class="fas fa-search search-icon" id="search-icon"></i>
        </div>
        
        <!-- 按钮区域 -->
        <div class="buttons-container">
            <button id="advanced-search-btn" class="btn btn-secondary">高级搜索</button>
            <a href="{{ url_for('sampling_inspection.new_inspection') }}" class="btn btn-success">新增记录</a>
        </div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <div class="filter-row">
        <div class="filter-group">
            <input type="text" class="filter-control" id="filter-report-code" placeholder="报告编号" value="{{ report_code or '' }}">
        </div>

        <div class="filter-group">
            <div class="multi-select-dropdown" id="supplier-dropdown">
                <button type="button" class="multi-select-button" tabindex="0">
                    <span class="multi-select-text">供应商</span>
                    <span class="multi-select-arrow">▼</span>
                </button>
                <div class="multi-select-options">
                    {% for supplier_item in suppliers %}
                    <div class="multi-select-option">
                        <input type="checkbox" id="supplier-{{ loop.index }}" value="{{ supplier_item }}"
                               {% if supplier and supplier_item in supplier.split(',') %}checked{% endif %}>
                        <label for="supplier-{{ loop.index }}">{{ supplier_item }}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="filter-group">
            <div class="multi-select-dropdown" id="inspection-type-dropdown">
                <button type="button" class="multi-select-button" tabindex="0">
                    <span class="multi-select-text">检验类型</span>
                    <span class="multi-select-arrow">▼</span>
                </button>
                <div class="multi-select-options">
                    {% for type_item in inspection_types %}
                    <div class="multi-select-option">
                        <input type="checkbox" id="type-{{ loop.index }}" value="{{ type_item }}"
                               {% if inspection_type and type_item in inspection_type.split(',') %}checked{% endif %}>
                        <label for="type-{{ loop.index }}">{{ inspection_type_map.get(type_item, type_item) }}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="filter-group">
            <div class="filter-number-group">
                <select class="filter-operator" id="filter-defect-rate-op" title="不良率比较方式">
                    <option value="gt" {% if defect_rate_op == 'gt' %}selected{% endif %}>大于</option>
                    <option value="gte" {% if defect_rate_op == 'gte' %}selected{% endif %}>大于等于</option>
                    <option value="lt" {% if defect_rate_op == 'lt' %}selected{% endif %}>小于</option>
                    <option value="lte" {% if defect_rate_op == 'lte' %}selected{% endif %}>小于等于</option>
                    <option value="eq" {% if defect_rate_op == 'eq' %}selected{% endif %}>等于</option>
                </select>
                <input type="number" class="filter-number" id="filter-defect-rate" placeholder="不良率%" step="0.01" min="0" max="100" value="{{ defect_rate or '' }}">
            </div>
        </div>

        <div class="filter-group">
            <div class="multi-select-dropdown" id="inspection-result-dropdown">
                <button type="button" class="multi-select-button" tabindex="0">
                    <span class="multi-select-text">检验结果</span>
                    <span class="multi-select-arrow">▼</span>
                </button>
                <div class="multi-select-options">
                    {% for result_item in inspection_results %}
                    <div class="multi-select-option">
                        <input type="checkbox" id="result-{{ loop.index }}" value="{{ result_item }}"
                               {% if inspection_result and result_item in inspection_result.split(',') %}checked{% endif %}>
                        <label for="result-{{ loop.index }}">{{ result_item }}</label>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="filter-actions">
            <button type="button" class="filter-btn filter-btn-primary" id="apply-filters">
                <i class="fas fa-search"></i> 筛选
            </button>
            <button type="button" class="filter-btn filter-btn-secondary" id="reset-filters">
                <i class="fas fa-undo"></i> 重置
            </button>
        </div>
    </div>
</div>

<!-- 表格容器 -->
<div class="table-container">
    <table class="sortable-table">
        <thead>
            <tr>
                <th data-sort="id">序号</th>
                <th data-sort="report_code">报告编码</th>
                <th data-sort="material_number">料号</th>
                <th data-sort="material_name">名称</th>
                <th data-sort="specification">规格</th>
                <th data-sort="supplier">供应商</th>
                <th data-sort="purchase_order">采购单号</th>
                <th data-sort="receipt_date">来料日期</th>
                <th data-sort="inspection_date">检验日期</th>
                <th data-sort="batch_number">批次号</th>
                <th data-sort="inspection_type">检验类型</th>
                <th data-sort="total_quantity">来料数量</th>
                <th data-sort="sample_quantity">检验数量</th>
                <th data-sort="defect_quantity">不良数量</th>
                <th data-sort="defect_rate">不良率</th>
                <th data-sort="inspection_result">检验结果</th>
                <th data-sort="defect_issues">问题点</th>
                <th data-sort="qualified_rate">合格率</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr>
                <td data-column="id">{{ (page - 1) * per_page + loop.index }}</td>
                <td data-column="report_code">{{ record.report_code or '-' }}</td>
                <td data-column="material_number">{{ record.material_number }}</td>
                <td data-column="material_name">{{ record.material_name }}</td>
                <td data-column="specification">{{ record.specification }}</td>
                <td data-column="supplier">{{ record.supplier }}</td>
                <td data-column="purchase_order">{{ record.purchase_order }}</td>
                <td data-column="receipt_date">{{ record.receipt_date }}</td>
                <td data-column="inspection_date">{{ record.inspection_date }}</td>
                <td data-column="batch_number">{{ record.batch_number or '' }}</td>
                <td data-column="inspection_type">
                    {% if record.inspection_type == 'sampling' %}
                        <span class="badge badge-primary">抽样检验</span>
                    {% elif record.inspection_type == 'full' %}
                        <span class="badge badge-warning">全部检验</span>
                    {% elif record.inspection_type == 'exempt' %}
                        <span class="badge badge-success">免检</span>
                    {% else %}
                        <span class="badge badge-secondary">抽样检验</span>
                    {% endif %}
                </td>
                <td data-column="total_quantity">{{ record.total_quantity }}</td>
                <td data-column="sample_quantity">{{ record.sample_quantity }}</td>
                <td data-column="defect_quantity">{{ record.defect_quantity }}</td>
                <td data-column="defect_rate">{{ "{:.2f}%".format((record.defect_quantity / record.sample_quantity * 100) if record.sample_quantity > 0 else 0) }}</td>
                <td data-column="inspection_result">{{ "合格" if record.defect_quantity == 0 else "不合格" }}</td>
                <td data-column="defect_issues">{{ record.defect_issues|truncate(10, true) if record.defect_issues else '-' }}</td>
                <td data-column="qualified_rate">{{ "{:.2f}%".format(((record.sample_quantity - record.defect_quantity) / record.sample_quantity * 100) if record.sample_quantity > 0 else 0) }}</td>
                <td>
                    <button class="btn btn-primary" onclick="viewInspectionDetails('{{ record.id }}', 'sampling')">详情</button>
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="17" style="text-align: center;">暂无抽样检验记录</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页和记录统计信息 -->
<div class="pagination-container" style="display: flex; align-items: center; justify-content: space-between; margin-top: 2px; padding: 2px 0; flex-wrap: nowrap;">
    <div class="summary" style="color: #666; white-space: nowrap; font-size: 11px; min-width: auto;">
        显示 {{ records|length }}/{{ total_records }}
    </div>

    <div class="pagination" style="display: flex; align-items: center; margin: 0; flex-grow: 1; justify-content: center;">
        {% if page > 1 %}
        <a href="{{ pagination_url(1) }}" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">首页</a>
        <a href="{{ pagination_url(page-1) }}" class="page-nav-btn prev-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">上一页</a>
        {% else %}
        <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">首页</span>
        <span class="disabled page-nav-btn prev-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">上一页</span>
        {% endif %}

        <span class="current-page-display" style="display: inline-block; margin: 0 1px; padding: 1px 8px; background-color: #1976d2; border: 1px solid #1976d2; color: white; min-width: 15px; text-align: center;">{{ page }}</span>

        {% if page < total_pages %}
        <a href="{{ pagination_url(page+1) }}" class="page-nav-btn next-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f1f1f1; border: 1px solid #ddd;">下一页</a>
        <a href="{{ pagination_url(total_pages) }}" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f1f1f1; border: 1px solid #ddd;">末页</a>
        {% else %}
        <span class="disabled page-nav-btn next-page-btn" style="display: inline-block; margin: 0 1px; padding: 1px 4px; min-width: 40px; text-align: center; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">下一页</span>
        <span class="disabled" style="display: inline-block; margin: 0 1px; padding: 1px 4px; background-color: #f9f9f9; border: 1px solid #ddd; color: #aaa;">末页</span>
        {% endif %}
    </div>
    
    <div class="page-size-selector" style="font-size: 11px; color: #666; display: flex; align-items: center; white-space: nowrap; min-width: auto; justify-content: flex-end;">
        每页:
        <select id="per-page-selector" style="padding: 0 2px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px; margin-left: 3px; height: 20px; width: 45px;">
            <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
            <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
            <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
            <option value="200" {% if per_page == 200 %}selected{% endif %}>200</option>
        </select>
    </div>
</div>

<!-- 记录详情模态框 -->
<div id="record-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="document.getElementById('record-modal').style.display='none'">&times;</span>
        <h2 style="font-size: 13px; margin: 0 0 5px 0; border-bottom: 1px solid #eee; padding-bottom: 3px;">检验记录详情</h2>
        <div class="record-details">
            <!-- 详情内容将由JavaScript动态填充 -->
        </div>
    </div>
</div>

<!-- 高级搜索模态框 -->
<div id="advanced-search-modal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeAdvancedSearchModal()">&times;</span>
        <h2 style="font-size: 13px; margin: 0 0 10px 0; border-bottom: 1px solid #eee; padding-bottom: 3px;">高级搜索</h2>
        
        <form id="advanced-search-form" method="get" action="{{ url_for('sampling_inspection.index') }}">
            <input type="hidden" name="date_type" id="advanced-date-type" value="{{ date_type }}">
            <div class="two-column-layout">
                <!-- 左侧列 -->
                <div class="column left-column">
                    <div class="form-group">
                        <div class="form-label">物料料号</div>
                        <div class="form-input">
                            <input type="text" name="material_number" value="{{ material_number or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">供应商</div>
                        <div class="form-input">
                            <input type="text" name="supplier" value="{{ supplier or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">采购单号</div>
                        <div class="form-input">
                            <input type="text" name="purchase_order" value="{{ purchase_order or '' }}" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">不良率</div>
                        <div class="form-input range-input">
                            <select name="defect_rate_op" class="form-control operator-select">
                                <option value="gt">大于</option>
                                <option value="lte">小于等于</option>
                            </select>
                            <input type="number" name="defect_rate" value="{{ defect_rate or '' }}" class="form-control" min="0" max="100" step="0.1">
                            <span class="unit-label">%</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">来料数量</div>
                        <div class="form-input range-input">
                            <select name="total_quantity_op" class="form-control operator-select">
                                <option value="gt">大于</option>
                                <option value="lte">小于等于</option>
                            </select>
                            <input type="number" name="total_quantity" value="{{ total_quantity or '' }}" class="form-control" min="0">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="form-label">检验结果</div>
                        <div class="form-input">
                            <input type="text" name="inspection_result" value="{{ inspection_result or '' }}" class="form-control">
                        </div>
                    </div>
                </div>
                
                <!-- 右侧列 -->
                <div class="column right-column">
                    <div class="date-group">
                        <div class="date-label">来料日期 <small style="color: #666; font-size: 11px;"></small></div>
                        <div class="date-inputs">
                            <div class="form-group">
                                <div class="form-label">开始</div>
                                <div class="form-input">
                                    <input type="date" name="receipt_start_date" value="{{ receipt_start_date or '' }}" class="form-control" id="receipt-start-date">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">结束</div>
                                <div class="form-input">
                                    <input type="date" name="receipt_end_date" value="{{ receipt_end_date or '' }}" class="form-control" id="receipt-end-date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="date-group">
                        <div class="date-label">检验日期 <small style="color: #666; font-size: 11px;"></small></div>
                        <div class="date-inputs">
                            <div class="form-group">
                                <div class="form-label">开始</div>
                                <div class="form-input">
                                    <input type="date" name="start_date" value="{{ start_date or '' }}" class="form-control" id="inspection-start-date">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="form-label">结束</div>
                                <div class="form-input">
                                    <input type="date" name="end_date" value="{{ end_date or '' }}" class="form-control" id="inspection-end-date">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="search-buttons">
                <button type="button" id="advanced-reset-btn" class="btn btn-secondary">重置</button>
                <button type="submit" class="btn btn-primary">搜索</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 日期范围选择器
        const dateRangeDisplay = document.getElementById('date-range-display');
        const dateRangePopup = document.getElementById('date-range-popup');
        const startDateInput = document.getElementById('start-date-input');
        const endDateInput = document.getElementById('end-date-input');
        const selectedDateRange = document.getElementById('selected-date-range');
        const applyDateRange = document.getElementById('confirm-date-range');
        const cancelDateRange = document.getElementById('cancel-date-range');
        const quickRangeBtns = document.querySelectorAll('.quick-range-btn');
        const dateTypeSelector = document.getElementById('date-type-selector');
        
        // 初始化检查
        console.log('DOM元素初始化状态:');
        console.log('dateRangeDisplay:', dateRangeDisplay);
        console.log('dateRangePopup:', dateRangePopup);
        console.log('startDateInput:', startDateInput);
        console.log('endDateInput:', endDateInput);
        console.log('selectedDateRange:', selectedDateRange);
        console.log('dateTypeSelector:', dateTypeSelector);
        
        if (!dateRangeDisplay || !dateRangePopup || !startDateInput ||
            !endDateInput || !selectedDateRange || !dateTypeSelector) {
            console.error('日期选择器DOM元素未找到！');
            console.error('缺失的元素:', {
                dateRangeDisplay: !!dateRangeDisplay,
                dateRangePopup: !!dateRangePopup,
                startDateInput: !!startDateInput,
                endDateInput: !!endDateInput,
                selectedDateRange: !!selectedDateRange,
                dateTypeSelector: !!dateTypeSelector
            });
            return;
        }
        
        // 日期类型选择器变化事件
        if (dateTypeSelector) {
            console.log('绑定日期类型选择器事件');

            // 确保元素可以交互
            dateTypeSelector.style.pointerEvents = 'auto';
            dateTypeSelector.style.position = 'relative';
            dateTypeSelector.style.zIndex = '100';

            dateTypeSelector.addEventListener('change', function() {
                console.log('日期类型选择器变化:', this.value);
                // 执行搜索
                window.location.href = buildSearchUrl();
            });

            // 添加点击事件作为备用
            dateTypeSelector.addEventListener('click', function() {
                console.log('日期类型选择器被点击');
            });

            console.log('日期类型选择器事件绑定完成');
        } else {
            console.error('日期类型选择器元素未找到！');
        }

        // 强制修复日期类型选择器的交互性
        function forceFixDateTypeSelector() {
            const selector = document.getElementById('date-type-selector');
            if (selector) {
                // 强制设置样式
                selector.style.pointerEvents = 'auto';
                selector.style.position = 'relative';
                selector.style.zIndex = '200';
                selector.style.cursor = 'pointer';
                selector.style.userSelect = 'auto';
                selector.style.webkitUserSelect = 'auto';

                // 移除可能阻止交互的属性
                selector.removeAttribute('disabled');
                selector.style.display = 'block';
                selector.style.visibility = 'visible';

                console.log('强制修复日期类型选择器完成');
                return true;
            }
            return false;
        }

        // 立即执行修复
        forceFixDateTypeSelector();

        // 延迟执行修复（防止其他代码覆盖）
        setTimeout(forceFixDateTypeSelector, 100);
        setTimeout(forceFixDateTypeSelector, 500);
        setTimeout(forceFixDateTypeSelector, 1000);

        // 筛选功能初始化
        initializeFilters();

        // 初始化多选下拉框
        initializeMultiSelectDropdowns();

        // 延迟修复筛选控件（确保在所有其他代码执行后）
        setTimeout(function() {
            const filterElements = {
                reportCode: document.getElementById('filter-report-code'),
                supplier: document.getElementById('filter-supplier'),
                inspectionType: document.getElementById('filter-inspection-type'),
                defectRateOp: document.getElementById('filter-defect-rate-op'),
                defectRate: document.getElementById('filter-defect-rate'),
                inspectionResult: document.getElementById('filter-inspection-result')
            };
            forceFixFilterControls(filterElements);
        }, 100);

        setTimeout(function() {
            const filterElements = {
                reportCode: document.getElementById('filter-report-code'),
                supplier: document.getElementById('filter-supplier'),
                inspectionType: document.getElementById('filter-inspection-type'),
                defectRateOp: document.getElementById('filter-defect-rate-op'),
                defectRate: document.getElementById('filter-defect-rate'),
                inspectionResult: document.getElementById('filter-inspection-result')
            };
            forceFixFilterControls(filterElements);
        }, 500);

        setTimeout(function() {
            const filterElements = {
                reportCode: document.getElementById('filter-report-code'),
                supplier: document.getElementById('filter-supplier'),
                inspectionType: document.getElementById('filter-inspection-type'),
                defectRateOp: document.getElementById('filter-defect-rate-op'),
                defectRate: document.getElementById('filter-defect-rate'),
                inspectionResult: document.getElementById('filter-inspection-result')
            };
            forceFixFilterControls(filterElements);
        }, 1000);

        // 多选下拉框功能实现
        function initializeMultiSelectDropdowns() {
            console.log('初始化多选下拉框');

            const dropdowns = document.querySelectorAll('.multi-select-dropdown');

            dropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('.multi-select-button');
                const options = dropdown.querySelector('.multi-select-options');
                const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
                const textSpan = dropdown.querySelector('.multi-select-text');

                // 初始化显示文本
                updateButtonText(dropdown);

                // 点击按钮切换下拉框
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    closeAllDropdowns();
                    dropdown.classList.toggle('open');
                });

                // 复选框变化事件
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        updateButtonText(dropdown);
                    });
                });

                // 阻止选项区域点击时关闭下拉框
                options.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });

            // 点击其他地方关闭所有下拉框
            document.addEventListener('click', function() {
                closeAllDropdowns();
            });

            console.log('多选下拉框初始化完成');
        }

        function closeAllDropdowns() {
            document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                dropdown.classList.remove('open');
            });
        }

        function updateButtonText(dropdown) {
            const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
            const textSpan = dropdown.querySelector('.multi-select-text');
            const defaultText = dropdown.id.includes('supplier') ? '供应商' :
                               dropdown.id.includes('inspection-type') ? '检验类型' : '检验结果';

            if (checkboxes.length === 0) {
                textSpan.innerHTML = defaultText;
            } else if (checkboxes.length === 1) {
                textSpan.innerHTML = checkboxes[0].nextElementSibling.textContent;
            } else {
                textSpan.innerHTML = `${defaultText} <span class="multi-select-count">${checkboxes.length}</span>`;
            }
        }

        // 筛选功能实现
        function initializeFilters() {
            console.log('初始化筛选功能');

            // 获取筛选元素
            const filterElements = {
                reportCode: document.getElementById('filter-report-code'),
                defectRateOp: document.getElementById('filter-defect-rate-op'),
                defectRate: document.getElementById('filter-defect-rate'),
                applyBtn: document.getElementById('apply-filters'),
                resetBtn: document.getElementById('reset-filters')
            };

            // 检查元素是否存在
            const missingElements = Object.entries(filterElements)
                .filter(([name, element]) => !element)
                .map(([name]) => name);

            if (missingElements.length > 0) {
                console.error('筛选元素未找到:', missingElements);
                return;
            }

            // 强制修复筛选控件的交互性
            forceFixFilterControls(filterElements);

            // 从URL参数初始化筛选值
            initializeFilterValues(filterElements);

            // 绑定筛选按钮事件
            filterElements.applyBtn.addEventListener('click', function() {
                console.log('应用筛选');
                applyFilters(filterElements);
            });

            // 绑定重置按钮事件
            filterElements.resetBtn.addEventListener('click', function() {
                console.log('重置筛选');
                resetFilters(filterElements);
            });

            // 绑定回车键事件
            [filterElements.reportCode, filterElements.defectRate].forEach(input => {
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            applyFilters(filterElements);
                        }
                    });
                }
            });

            console.log('筛选功能初始化完成');
        }

        // 强制修复筛选控件的交互性
        function forceFixFilterControls(elements) {
            console.log('开始强制修复筛选控件交互性');

            // 修复所有筛选控件
            Object.entries(elements).forEach(([name, element]) => {
                if (element && name !== 'applyBtn' && name !== 'resetBtn') {
                    // 强制设置交互样式
                    element.style.pointerEvents = 'auto';
                    element.style.position = 'relative';
                    element.style.zIndex = '101';

                    // 移除可能阻止交互的属性
                    element.removeAttribute('disabled');
                    element.style.display = 'block';
                    element.style.visibility = 'visible';

                    // 根据元素类型设置cursor
                    if (element.tagName === 'SELECT') {
                        element.style.cursor = 'pointer';
                    } else if (element.tagName === 'INPUT') {
                        element.style.cursor = 'text';
                    }

                    console.log(`修复控件: ${name} (${element.tagName})`);
                }
            });

            // 额外修复：查找所有筛选区域内的控件
            const filterSection = document.querySelector('.filter-section');
            if (filterSection) {
                const allControls = filterSection.querySelectorAll('input, select');
                allControls.forEach((control, index) => {
                    control.style.pointerEvents = 'auto';
                    control.style.position = 'relative';
                    control.style.zIndex = '101';
                    control.removeAttribute('disabled');

                    if (control.tagName === 'SELECT') {
                        control.style.cursor = 'pointer';
                    } else if (control.tagName === 'INPUT') {
                        control.style.cursor = 'text';
                    }

                    console.log(`额外修复控件 ${index}: ${control.tagName} ${control.id || control.className}`);
                });
            }

            console.log('筛选控件交互性修复完成');
        }

        // 从URL参数初始化筛选值
        function initializeFilterValues(elements) {
            const urlParams = new URLSearchParams(window.location.search);

            // 设置各个筛选字段的值
            if (elements.reportCode && urlParams.get('report_code')) {
                elements.reportCode.value = urlParams.get('report_code');
            }

            if (elements.supplier && urlParams.get('supplier')) {
                elements.supplier.value = urlParams.get('supplier');
            }

            if (elements.inspectionType && urlParams.get('inspection_type')) {
                elements.inspectionType.value = urlParams.get('inspection_type');
            }

            if (elements.defectRateOp && urlParams.get('defect_rate_op')) {
                elements.defectRateOp.value = urlParams.get('defect_rate_op');
            }

            if (elements.defectRate && urlParams.get('defect_rate')) {
                elements.defectRate.value = urlParams.get('defect_rate');
            }

            if (elements.inspectionResult && urlParams.get('inspection_result')) {
                elements.inspectionResult.value = urlParams.get('inspection_result');
            }
        }

        // 获取多选下拉框的选中值
        function getMultiSelectValues(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (!dropdown) return [];

            const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 应用筛选
        function applyFilters(elements) {
            const params = new URLSearchParams();

            // 保留现有的日期和分页参数
            const urlParams = new URLSearchParams(window.location.search);
            ['start_date', 'end_date', 'date_type', 'per_page'].forEach(key => {
                if (urlParams.get(key)) {
                    params.set(key, urlParams.get(key));
                }
            });

            // 添加筛选参数
            if (elements.reportCode.value.trim()) {
                params.set('report_code', elements.reportCode.value.trim());
            }

            // 获取多选值
            const supplierValues = getMultiSelectValues('supplier-dropdown');
            if (supplierValues.length > 0) {
                params.set('supplier', supplierValues.join(','));
            }

            const inspectionTypeValues = getMultiSelectValues('inspection-type-dropdown');
            if (inspectionTypeValues.length > 0) {
                params.set('inspection_type', inspectionTypeValues.join(','));
            }

            const inspectionResultValues = getMultiSelectValues('inspection-result-dropdown');
            if (inspectionResultValues.length > 0) {
                params.set('inspection_result', inspectionResultValues.join(','));
            }

            if (elements.defectRate.value) {
                params.set('defect_rate_op', elements.defectRateOp.value);
                params.set('defect_rate', elements.defectRate.value);
            }

            // 重置到第一页
            params.set('page', '1');

            // 跳转到新URL
            const baseUrl = window.location.pathname;
            window.location.href = `${baseUrl}?${params.toString()}`;
        }

        // 重置多选下拉框
        function resetMultiSelectDropdowns() {
            document.querySelectorAll('.multi-select-dropdown').forEach(dropdown => {
                const checkboxes = dropdown.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                updateButtonText(dropdown);
            });
        }

        // 重置筛选
        function resetFilters(elements) {
            // 清空所有筛选字段
            elements.reportCode.value = '';
            elements.defectRateOp.value = 'gt';
            elements.defectRate.value = '';

            // 重置多选下拉框
            resetMultiSelectDropdowns();

            // 保留日期参数，清除筛选参数
            const urlParams = new URLSearchParams(window.location.search);
            const params = new URLSearchParams();

            ['start_date', 'end_date', 'date_type', 'per_page'].forEach(key => {
                if (urlParams.get(key)) {
                    params.set(key, urlParams.get(key));
                }
            });

            // 重置到第一页
            params.set('page', '1');

            // 跳转到新URL
            const baseUrl = window.location.pathname;
            window.location.href = `${baseUrl}?${params.toString()}`;
        }

        // 格式化日期为YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 格式化日期为YYYYMMDD
        function formatDateShort(date) {
            const year = date.getFullYear().toString();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // 设置默认日期范围（近7天）
        function setDefaultDateRange() {
            // 从URL参数获取日期
            const urlParams = new URLSearchParams(window.location.search);
            const urlStartDate = urlParams.get('start_date');
            const urlEndDate = urlParams.get('end_date');
            const urlDateType = urlParams.get('date_type');
            
            // 设置日期类型
            if (urlDateType) {
                dateTypeSelector.value = urlDateType;
            }
            
            if (urlStartDate && urlEndDate) {
                startDateInput.value = urlStartDate;
                endDateInput.value = urlEndDate;
                
                // 更新显示文本 - 始终使用YYYY-MM-DD ~ YYYY-MM-DD格式
                const startDate = new Date(urlStartDate);
                const endDate = new Date(urlEndDate);
                selectedDateRange.textContent = `${formatDateShort(startDate)} ~ ${formatDateShort(endDate)}`;
            } else {
                const today = new Date();
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(today.getDate() - 7);
                
                startDateInput.value = formatDate(sevenDaysAgo);
                endDateInput.value = formatDate(today);
                selectedDateRange.textContent = `${formatDateShort(sevenDaysAgo)} ~ ${formatDateShort(today)}`;
            }
        }
        
        // 初始化设置默认日期
        setDefaultDateRange();

        // 更新显示文本
        updateDateDisplay();

        // 初始化日历
        generateCalendars();
        console.log('Calendars generated');
        
        // 显示/隐藏日期选择弹窗
        dateRangeDisplay.addEventListener('click', function() {
            if (dateRangePopup.style.display === 'block') {
                dateRangePopup.style.display = 'none';
            } else {
                dateRangePopup.style.display = 'block';
                // 每次显示时重新生成日历
                generateCalendars();
                console.log('Calendar popup opened, regenerated calendars');
            }
        });
        
        // 点击快速选择按钮
        quickRangeBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const days = parseInt(this.getAttribute('data-days'));
                const today = new Date();
                const pastDate = new Date();
                pastDate.setDate(today.getDate() - days);
                
                // 确保不超过3年
                const threeYearsAgo = new Date();
                threeYearsAgo.setFullYear(today.getFullYear() - 3);
                
                const startDate = days > 365*3 ? threeYearsAgo : pastDate;
                
                startDateInput.value = formatDate(startDate);
                endDateInput.value = formatDate(today);
                
                // 更新显示文本
                updateDateDisplay();
                
                // 隐藏弹窗
                dateRangePopup.style.display = 'none';
                
                // 直接执行搜索，不需要点击确认按钮
                console.log('Quick range selected and executing search:', days, 'days');
                window.location.href = buildSearchUrl();
            });
        });
        
        // 应用日期范围和确认按钮
        applyDateRange.addEventListener('click', function() {
            console.log('Confirm button clicked');
            if (startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                // 比较日期（允许选择今天作为结束日期）
                const endDateTime = new Date(endDateInput.value);
                endDateTime.setHours(0, 0, 0, 0);
                const todayTime = new Date();
                todayTime.setHours(0, 0, 0, 0);
                
                // 检查结束日期是否超过今天（但允许等于今天）
                if (endDateTime > todayTime) {
                    alert('结束日期不能超过今天');
                    return;
                }
                
                // 检查日期范围是否超过3年
                const threeYearsAgo = new Date();
                threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
                
                if (startDate < threeYearsAgo) {
                    alert('搜索时间范围不能超过3年');
                    startDateInput.value = formatDate(threeYearsAgo);
                    return;
                }
                
                // 隐藏弹窗
                dateRangePopup.style.display = 'none';
                
                // 更新显示文本
                updateDateDisplay();
                
                // 执行搜索
                console.log('Executing search with date range:', startDateInput.value, endDateInput.value);
                window.location.href = buildSearchUrl();
            } else if (!startDateInput.value && !endDateInput.value) {
                alert('请至少选择一个日期');
            } else {
                // 隐藏弹窗
                dateRangePopup.style.display = 'none';
                
                // 更新显示文本
                updateDateDisplay();
                
                // 执行搜索
                console.log('Executing search with partial date range');
                window.location.href = buildSearchUrl();
            }
        });
        
        // 移除重复的确认按钮逻辑
        // document.getElementById('confirm-date-range').addEventListener('click', function() {
        //     ...
        // });
        
        // 取消按钮
        cancelDateRange.addEventListener('click', function() {
            dateRangePopup.style.display = 'none';
        });
        
        // 点击其他地方关闭弹窗 - 移除此行为，仅通过按钮关闭
        // document.addEventListener('click', function(event) {
        //     const isClickInside = dateRangeDisplay.contains(event.target) || dateRangePopup.contains(event.target);
        //     if (!isClickInside && dateRangePopup.style.display === 'block') {
        //         dateRangePopup.style.display = 'none';
        //     }
        // });
        
        // 确保只有点击取消按钮或确认按钮时才关闭弹窗
        cancelDateRange.addEventListener('click', function() {
            console.log('Cancel button clicked');
            dateRangePopup.style.display = 'none';
        });
        
        // 构建搜索URL
        function buildSearchUrl() {
            const baseUrl = "{{ url_for('sampling_inspection.index') }}";
            const params = new URLSearchParams();
            
            // 添加日期参数
            if (startDateInput.value) {
                params.append('start_date', startDateInput.value);
            }
            
            if (endDateInput.value) {
                params.append('end_date', endDateInput.value);
            }

            // 添加日期类型参数
            params.append('date_type', dateTypeSelector.value);
            
            // 添加快速搜索参数
            const quickSearch = document.getElementById('quick-search-input').value.trim();
            if (quickSearch) {
                params.append('quick_search', quickSearch);
            }
            
            // 保留其他现有参数（除了page和per_page）
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.forEach((value, key) => {
                if (key !== 'page' && key !== 'per_page' && key !== 'start_date' && key !== 'end_date' && key !== 'quick_search' && key !== 'date_type') {
                    params.append(key, value);
                }
            });
            
            // 添加页码参数（默认为第1页）
            params.append('page', 1);
            
            return `${baseUrl}?${params.toString()}`;
        }
        
        // 获取搜索相关元素
        const quickSearchInput = document.getElementById('quick-search-input');
        const searchIcon = document.getElementById('search-icon');
        
        // 高级搜索按钮 - 已移动到下方的setupDateValidation函数调用中

        // 高级搜索表单提交
        document.getElementById('advanced-search-form').addEventListener('submit', function(e) {
            // 更新高级搜索表单中的日期类型
            document.getElementById('advanced-date-type').value = dateTypeSelector.value;
        });
        
        // 定义执行搜索的函数
        function performSearch() {
            window.location.href = buildSearchUrl();
        }
        
        // 搜索图标点击事件
        searchIcon.addEventListener('click', performSearch);
        
        // 快速搜索回车键
        quickSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // 确保搜索框可以获得焦点
        quickSearchInput.addEventListener('click', function(e) {
            e.stopPropagation();
            this.focus();
        });

        // 快速搜索按钮点击事件
        // quickSearchBtn.addEventListener('click', performSearch); // This line was removed as per the edit hint
        
        // 高级搜索重置按钮
        document.getElementById('advanced-reset-btn').addEventListener('click', function() {
            const form = document.getElementById('advanced-search-form');
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.value = '';
            });
        });

        // 检验时间变化时自动调整来料时间
        function setupDateValidation() {
            const inspectionStartDate = document.getElementById('inspection-start-date');
            const inspectionEndDate = document.getElementById('inspection-end-date');
            const receiptStartDate = document.getElementById('receipt-start-date');
            const receiptEndDate = document.getElementById('receipt-end-date');

            // 当检验开始日期变化时
            if (inspectionStartDate) {
                inspectionStartDate.addEventListener('change', function() {
                    const inspectionStart = new Date(this.value);
                    if (this.value && !isNaN(inspectionStart)) {
                        // 总是自动设置来料开始日期为检验开始日期前7天
                        const suggestedReceiptStart = new Date(inspectionStart);
                        suggestedReceiptStart.setDate(inspectionStart.getDate() - 7);
                        receiptStartDate.value = suggestedReceiptStart.toISOString().split('T')[0];

                        // 如果有检验结束日期，来料结束日期应该与检验结束日期一致
                        const inspectionEndValue = inspectionEndDate.value;
                        if (inspectionEndValue) {
                            receiptEndDate.value = inspectionEndValue;
                        } else {
                            // 如果没有检验结束日期，来料结束日期与检验开始日期一致
                            receiptEndDate.value = this.value;
                        }
                    }
                });
            }

            // 当检验结束日期变化时
            if (inspectionEndDate) {
                inspectionEndDate.addEventListener('change', function() {
                    const inspectionEnd = new Date(this.value);
                    if (this.value && !isNaN(inspectionEnd)) {
                        // 设置来料结束日期与检验结束日期一致
                        receiptEndDate.value = this.value;

                        // 如果来料开始日期为空或晚于来料结束日期，则调整来料开始日期
                        const receiptStart = receiptStartDate.value ? new Date(receiptStartDate.value) : null;
                        const receiptEnd = new Date(this.value);
                        if (!receiptStart || receiptStart >= receiptEnd) {
                            const suggestedReceiptStart = new Date(receiptEnd);
                            suggestedReceiptStart.setDate(receiptEnd.getDate() - 7);
                            receiptStartDate.value = suggestedReceiptStart.toISOString().split('T')[0];
                        }
                    }
                });
            }

            // 来料日期变化时的验证
            if (receiptStartDate) {
                receiptStartDate.addEventListener('change', function() {
                    validateReceiptDate(this, '开始');
                });
            }

            if (receiptEndDate) {
                receiptEndDate.addEventListener('change', function() {
                    validateReceiptDate(this, '结束');
                });
            }

            // 验证来料日期是否在检验日期之前
            function validateReceiptDate(dateInput, dateType) {
                const receiptDate = new Date(dateInput.value);
                const inspectionStart = inspectionStartDate.value ? new Date(inspectionStartDate.value) : null;
                const inspectionEnd = inspectionEndDate.value ? new Date(inspectionEndDate.value) : null;

                // 清除之前的样式
                dateInput.classList.remove('date-warning', 'date-error');

                if (dateInput.value && !isNaN(receiptDate)) {
                    let shouldAdjust = false;
                    let warningMessage = '';

                    // 检查是否晚于检验开始日期
                    if (inspectionStart && receiptDate >= inspectionStart) {
                        shouldAdjust = true;
                        warningMessage = `来料${dateType}日期不能晚于检验开始日期`;
                        dateInput.classList.add('date-error');
                    }
                    // 检查是否晚于检验结束日期
                    else if (inspectionEnd && receiptDate >= inspectionEnd) {
                        shouldAdjust = true;
                        warningMessage = `来料${dateType}日期不能晚于检验结束日期`;
                        dateInput.classList.add('date-error');
                    }

                    if (shouldAdjust) {
                        // 显示警告并自动调整
                        const confirmAdjust = confirm(`${warningMessage}，是否自动调整为合理日期？`);
                        if (confirmAdjust) {
                            const referenceDate = inspectionStart || inspectionEnd;
                            const adjustedDate = new Date(referenceDate);
                            adjustedDate.setDate(referenceDate.getDate() - (dateType === '开始' ? 7 : 1));
                            dateInput.value = adjustedDate.toISOString().split('T')[0];
                            dateInput.classList.remove('date-error');
                            dateInput.classList.add('date-warning');

                            // 3秒后移除警告样式
                            setTimeout(() => {
                                dateInput.classList.remove('date-warning');
                            }, 3000);
                        }
                    }
                }
            }

            // 添加实时日期验证提示
            function addDateValidationHints() {
                [receiptStartDate, receiptEndDate].forEach(input => {
                    if (input) {
                        input.addEventListener('input', function() {
                            // 清除之前的样式
                            this.classList.remove('date-warning', 'date-error');

                            if (this.value) {
                                const receiptDate = new Date(this.value);
                                const inspectionStart = inspectionStartDate.value ? new Date(inspectionStartDate.value) : null;
                                const inspectionEnd = inspectionEndDate.value ? new Date(inspectionEndDate.value) : null;

                                // 实时检查日期合理性
                                if (inspectionStart && receiptDate >= inspectionStart) {
                                    this.classList.add('date-error');
                                } else if (inspectionEnd && receiptDate >= inspectionEnd) {
                                    this.classList.add('date-error');
                                }
                            }
                        });
                    }
                });
            }

            // 调用实时验证
            addDateValidationHints();

            // 初始化日期设置
            function initializeDates() {
                // 如果有检验日期但没有来料日期，自动设置来料日期
                const inspectionStart = inspectionStartDate.value ? new Date(inspectionStartDate.value) : null;
                const inspectionEnd = inspectionEndDate.value ? new Date(inspectionEndDate.value) : null;
                const receiptStart = receiptStartDate.value ? new Date(receiptStartDate.value) : null;
                const receiptEnd = receiptEndDate.value ? new Date(receiptEndDate.value) : null;

                // 如果有检验开始日期
                if (inspectionStart && (!receiptStart || receiptStart >= inspectionStart)) {
                    const suggestedReceiptStart = new Date(inspectionStart);
                    suggestedReceiptStart.setDate(inspectionStart.getDate() - 7);
                    receiptStartDate.value = suggestedReceiptStart.toISOString().split('T')[0];
                }

                // 来料结束日期的设置优先级：检验结束日期 > 检验开始日期
                if (inspectionEnd) {
                    // 如果有检验结束日期，来料结束日期与检验结束日期一致
                    receiptEndDate.value = inspectionEnd.toISOString().split('T')[0];
                } else if (inspectionStart) {
                    // 如果只有检验开始日期，来料结束日期与检验开始日期一致
                    receiptEndDate.value = inspectionStart.toISOString().split('T')[0];
                }

                // 确保来料开始日期不晚于来料结束日期
                const newReceiptStart = receiptStartDate.value ? new Date(receiptStartDate.value) : null;
                const newReceiptEnd = receiptEndDate.value ? new Date(receiptEndDate.value) : null;
                if (newReceiptStart && newReceiptEnd && newReceiptStart > newReceiptEnd) {
                    const adjustedStart = new Date(newReceiptEnd);
                    adjustedStart.setDate(newReceiptEnd.getDate() - 7);
                    receiptStartDate.value = adjustedStart.toISOString().split('T')[0];
                }
            }

            // 调用初始化
            initializeDates();


        }

        // 关闭高级搜索模态框的函数
        function closeAdvancedSearchModal() {
            const modal = document.getElementById('advanced-search-modal');
            modal.style.display = 'none';
            modal.classList.remove('show');
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('advanced-search-modal');
            if (event.target === modal) {
                closeAdvancedSearchModal();
            }
        });

        // 在高级搜索模态框打开时设置日期验证
        document.getElementById('advanced-search-btn').addEventListener('click', function() {
            const modal = document.getElementById('advanced-search-modal');
            modal.style.display = 'block';
            modal.classList.add('show');
            // 确保模态框可以交互
            modal.style.pointerEvents = 'auto';
            modal.style.zIndex = '10000';

            // 更新高级搜索表单中的日期类型
            document.getElementById('advanced-date-type').value = dateTypeSelector.value;

            // 设置日期验证和初始化
            setTimeout(() => {
                setupDateValidation();

                // 每次打开模态框时重新检查和调整日期
                const inspectionStartInput = document.getElementById('inspection-start-date');
                const inspectionEndInput = document.getElementById('inspection-end-date');
                const receiptStartInput = document.getElementById('receipt-start-date');
                const receiptEndInput = document.getElementById('receipt-end-date');

                if (inspectionStartInput && inspectionEndInput && receiptStartInput && receiptEndInput) {
                    const inspectionStart = inspectionStartInput.value ? new Date(inspectionStartInput.value) : null;
                    const inspectionEnd = inspectionEndInput.value ? new Date(inspectionEndInput.value) : null;
                    const receiptStart = receiptStartInput.value ? new Date(receiptStartInput.value) : null;
                    const receiptEnd = receiptEndInput.value ? new Date(receiptEndInput.value) : null;

                    // 如果有检验开始日期，且来料开始日期为空或不合理
                    if (inspectionStart && (!receiptStart || receiptStart >= inspectionStart)) {
                        const suggestedReceiptStart = new Date(inspectionStart);
                        suggestedReceiptStart.setDate(inspectionStart.getDate() - 7);
                        receiptStartInput.value = suggestedReceiptStart.toISOString().split('T')[0];
                    }

                    // 来料结束日期的设置优先级：检验结束日期 > 检验开始日期
                    if (inspectionEnd) {
                        // 如果有检验结束日期，来料结束日期与检验结束日期一致
                        receiptEndInput.value = inspectionEnd.toISOString().split('T')[0];
                    } else if (inspectionStart) {
                        // 如果只有检验开始日期，来料结束日期与检验开始日期一致
                        receiptEndInput.value = inspectionStart.toISOString().split('T')[0];
                    }

                    // 确保来料开始日期不晚于来料结束日期
                    const newReceiptStart = receiptStartInput.value ? new Date(receiptStartInput.value) : null;
                    const newReceiptEnd = receiptEndInput.value ? new Date(receiptEndInput.value) : null;
                    if (newReceiptStart && newReceiptEnd && newReceiptStart > newReceiptEnd) {
                        const adjustedStart = new Date(newReceiptEnd);
                        adjustedStart.setDate(newReceiptEnd.getDate() - 7);
                        receiptStartInput.value = adjustedStart.toISOString().split('T')[0];
                    }
                }
            }, 100); // 延迟执行确保DOM已更新
        });
        
        // 关闭模态框
        const closeButtons = document.querySelectorAll('.close');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.modal').style.display = 'none';
            });
        });
        
        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 页面大小选择器事件监听
        const perPageSelector = document.getElementById('per-page-selector');
        if (perPageSelector) {
            perPageSelector.addEventListener('change', function() {
                const perPage = this.value;
                const params = new URLSearchParams(window.location.search);
                params.set('per_page', perPage);
                params.set('page', '1'); // 重置到第一页
                window.location.href = window.location.pathname + '?' + params.toString();
            });
        }

        // 定义 viewInspectionDetails 函数
        function viewInspectionDetails(recordId, inspectionType = 'sampling') {
            console.log('查看检验详情:', recordId, inspectionType);
            // 直接跳转到详情页面
            window.location.href = `/incoming/detail_inspection?id=${recordId}&type=${inspectionType}`;
        }

        // 确保函数在全局作用域中可用
        window.viewInspectionDetails = viewInspectionDetails;

        // 生成日历
        function generateCalendars() {
            const calendarContainer = document.querySelector('.calendar-container');
            if (!calendarContainer) return;
            
            // 清空现有内容
            calendarContainer.innerHTML = '';
            
            // 获取当前选择的日期
            let startDate = startDateInput.value ? new Date(startDateInput.value) : null;
            let endDate = endDateInput.value ? new Date(endDateInput.value) : null;
            
            // 设置左右日历显示的月份：左侧显示当前月，右侧也显示当前月（默认情况下）
            const today = new Date();
            const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            
            // 如果有选中日期，则考虑显示选中日期所在月份
            let leftCalendarMonth = currentMonth;
            let rightCalendarMonth = currentMonth;
            
            // 创建日历
            const leftCalendar = createCalendar(leftCalendarMonth, startDate, endDate, 'start');
            const rightCalendar = createCalendar(rightCalendarMonth, startDate, endDate, 'end');
            
            calendarContainer.appendChild(leftCalendar);
            calendarContainer.appendChild(rightCalendar);
        }

        // 创建单个月份的日历
        function createCalendar(month, startDate, endDate, calendarType) {
            const calendar = document.createElement('div');
            calendar.className = 'calendar';
            calendar.dataset.type = calendarType;
            
            // 添加日期类型标签（开始日期/结束日期）
            const calendarLabel = document.createElement('div');
            calendarLabel.className = 'calendar-label';
            calendarLabel.textContent = calendarType === 'start' ? '开始日期' : '结束日期';
            calendar.appendChild(calendarLabel);
            
            // 创建月份标题
            const header = document.createElement('div');
            header.className = 'calendar-header';
            
            // 添加上一个月/下一个月导航按钮
            const prevBtn = document.createElement('span');
            prevBtn.className = 'calendar-nav prev';
            prevBtn.innerHTML = '&lt;&lt;';
            prevBtn.addEventListener('click', () => changeMonth(calendar, -1));
            
            const nextBtn = document.createElement('span');
            nextBtn.className = 'calendar-nav next';
            nextBtn.innerHTML = '&gt;&gt;';
            nextBtn.addEventListener('click', () => changeMonth(calendar, 1));
            
            // 月份标题
            const monthTitle = document.createElement('span');
            monthTitle.className = 'calendar-title';
            monthTitle.textContent = `${month.getFullYear()} 年 ${month.getMonth() + 1} 月`;
            
            header.appendChild(prevBtn);
            header.appendChild(monthTitle);
            header.appendChild(nextBtn);
            calendar.appendChild(header);
            
            // 创建星期标题
            const weekdayHeader = document.createElement('div');
            weekdayHeader.className = 'calendar-weekdays';
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            weekdays.forEach(day => {
                const dayElem = document.createElement('div');
                dayElem.className = 'weekday';
                dayElem.textContent = day;
                weekdayHeader.appendChild(dayElem);
            });
            calendar.appendChild(weekdayHeader);
            
            // 创建日期网格
            const daysGrid = document.createElement('div');
            daysGrid.className = 'calendar-days';
            
            // 计算当月第一天是星期几
            const firstDay = new Date(month.getFullYear(), month.getMonth(), 1).getDay();
            
            // 计算当月有多少天
            const daysInMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0).getDate();
            
            // 添加前导空白格
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'day empty';
                daysGrid.appendChild(emptyDay);
            }
            
            // 获取当天日期
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            // 添加日期
            for (let i = 1; i <= daysInMonth; i++) {
                const day = document.createElement('div');
                day.className = 'day';
                day.textContent = i;
                
                // 添加日期属性
                const currentDate = new Date(month.getFullYear(), month.getMonth(), i);
                const dateStr = formatDate(currentDate);
                day.dataset.date = dateStr;
                
                // 检查是否是今天 - 在右侧日历中显示
                if (currentDate.toDateString() === today.toDateString() && calendarType === 'end') {
                    day.classList.add('today');
                }
                
                // 根据日历类型添加不同的选择样式
                if (calendarType === 'start' && startDate && 
                    currentDate.getDate() === startDate.getDate() && 
                    currentDate.getMonth() === startDate.getMonth() && 
                    currentDate.getFullYear() === startDate.getFullYear()) {
                    day.classList.add('start-date');
                }
                
                if (calendarType === 'end' && endDate && 
                    currentDate.getDate() === endDate.getDate() && 
                    currentDate.getMonth() === endDate.getMonth() && 
                    currentDate.getFullYear() === endDate.getFullYear()) {
                    day.classList.add('end-date');
                }
                
                // 禁用将来日期作为结束日期（但允许选择今天）
                const isFutureDate = currentDate > today && 
                    !(currentDate.getDate() === today.getDate() && 
                      currentDate.getMonth() === today.getMonth() && 
                      currentDate.getFullYear() === today.getFullYear());
                      
                if (calendarType === 'end' && isFutureDate) {
                    day.classList.add('disabled');
                    day.title = '结束日期不能超过今天';
                } else {
                    // 添加点击事件 - 使用闭包保留当前值
                    day.onclick = (function(ds, ct, cd) {
                        return function() {
                            // 如果是禁用状态，则不响应点击
                            if (this.classList.contains('disabled')) {
                                return;
                            }
                            console.log('Day clicked:', ds, ct);
                            selectDate(ds, ct);
                        };
                    })(dateStr, calendarType, currentDate);
                }
                
                daysGrid.appendChild(day);
            }
            
            calendar.appendChild(daysGrid);
            return calendar;
        }

        // 更新日期显示
        function updateDateDisplay() {
            if (startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                selectedDateRange.textContent = `${formatDateShort(startDate)} ~ ${formatDateShort(endDate)}`;
                console.log('Updated date display:', selectedDateRange.textContent);
            } else if (startDateInput.value) {
                const startDate = new Date(startDateInput.value);
                selectedDateRange.textContent = `${formatDateShort(startDate)} ~ ?`;
            } else if (endDateInput.value) {
                const endDate = new Date(endDateInput.value);
                selectedDateRange.textContent = `? ~ ${formatDateShort(endDate)}`;
            } else {
                selectedDateRange.textContent = `请选择日期范围`;
            }
        }
        
        // 选择日期
        function selectDate(dateStr, calendarType) {
            console.log('selectDate called', dateStr, calendarType);
            const date = new Date(dateStr);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            // 防止选择未来日期作为结束日期（但允许选择今天）
            if (calendarType === 'end') {
                const selectedDate = new Date(dateStr);
                selectedDate.setHours(0, 0, 0, 0);
                
                if (selectedDate > today) {
                    alert('结束日期不能超过今天');
                    return;
                }
            }
            
            // 根据日历类型设置开始日期或结束日期
            if (calendarType === 'start') {
                // 在左侧日历选择日期，只设置开始日期
                startDateInput.value = dateStr;
                console.log('Set start date to', dateStr);
            } else {
                // 在右侧日历选择日期，只设置结束日期
                endDateInput.value = dateStr;
                console.log('Set end date to', dateStr);
            }
            
            // 检查日期逻辑顺序，如果开始日期晚于结束日期，显示警告
            if (startDateInput.value && endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(endDateInput.value);
                
                if (startDate > endDate) {
                    alert('开始日期不能晚于结束日期');
                    // 重置为当前选择的日期
                    if (calendarType === 'start') {
                        endDateInput.value = dateStr;
                        console.log('Adjusted end date to match start date', dateStr);
                    } else {
                        startDateInput.value = dateStr;
                        console.log('Adjusted start date to match end date', dateStr);
                    }
                }
            }
            
            // 更新日历上的显示状态，但不隐藏日历弹窗
            updateDateDisplay();
            
            // 重新生成日历以更新选择状态
            generateCalendars();
        }

        // 切换月份
        function changeMonth(calendar, direction) {
            const title = calendar.querySelector('.calendar-title');
            const [year, month] = title.textContent.match(/(\d+)/g);
            const calendarType = calendar.dataset.type;
            
            let newMonth = parseInt(month) - 1 + direction;
            let newYear = parseInt(year);
            
            if (newMonth < 0) {
                newMonth = 11;
                newYear--;
            } else if (newMonth > 11) {
                newMonth = 0;
                newYear++;
            }
            
            const newDate = new Date(newYear, newMonth, 1);
            const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
            
            // 替换日历
            const newCalendar = createCalendar(newDate, startDate, endDate, calendarType);
            calendar.parentNode.replaceChild(newCalendar, calendar);
        }
    });
</script>
{% endblock %} 