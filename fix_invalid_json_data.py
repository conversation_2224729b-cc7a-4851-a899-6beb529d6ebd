#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库中无效的JSON数据
解决JSON_EXTRACT函数报错的问题
"""

import mysql.connector
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def get_db_connection():
    """建立数据库连接"""
    config = Config()
    return mysql.connector.connect(
        host=config.DB_HOST,
        user=config.DB_USER,
        password=config.DB_PASSWORD,
        database=config.DB_NAME
    )

def check_and_fix_json_data():
    """检查并修复无效的JSON数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("开始检查sampling_inspection表中的defect_issues字段...")
        
        # 查找所有有defect_issues数据的记录
        cursor.execute("""
            SELECT id, defect_issues 
            FROM sampling_inspection 
            WHERE defect_issues IS NOT NULL AND defect_issues != ''
        """)
        
        records = cursor.fetchall()
        print(f"找到 {len(records)} 条包含defect_issues数据的记录")
        
        invalid_count = 0
        fixed_count = 0
        
        for record in records:
            record_id = record['id']
            defect_issues = record['defect_issues']
            
            # 检查JSON是否有效
            try:
                # 尝试解析JSON
                json.loads(defect_issues)
                print(f"记录 {record_id}: JSON数据有效")
            except (json.JSONDecodeError, TypeError) as e:
                invalid_count += 1
                print(f"记录 {record_id}: JSON数据无效 - {e}")
                print(f"原始数据: {repr(defect_issues)}")
                
                # 尝试修复数据
                fixed_data = fix_json_data(defect_issues)
                if fixed_data is not None:
                    # 更新数据库
                    cursor.execute("""
                        UPDATE sampling_inspection 
                        SET defect_issues = %s 
                        WHERE id = %s
                    """, (fixed_data, record_id))
                    fixed_count += 1
                    print(f"记录 {record_id}: 已修复")
                else:
                    # 如果无法修复，设置为空的JSON数组
                    cursor.execute("""
                        UPDATE sampling_inspection 
                        SET defect_issues = '[]' 
                        WHERE id = %s
                    """, (record_id,))
                    fixed_count += 1
                    print(f"记录 {record_id}: 已设置为空数组")
        
        # 提交更改
        conn.commit()
        
        print(f"\n修复完成:")
        print(f"- 总记录数: {len(records)}")
        print(f"- 无效JSON记录数: {invalid_count}")
        print(f"- 已修复记录数: {fixed_count}")
        
        # 验证修复结果
        print("\n验证修复结果...")
        cursor.execute("""
            SELECT COUNT(*) as count
            FROM sampling_inspection 
            WHERE defect_issues IS NOT NULL 
            AND defect_issues != '' 
            AND NOT JSON_VALID(defect_issues)
        """)
        
        remaining_invalid = cursor.fetchone()['count']
        print(f"剩余无效JSON记录数: {remaining_invalid}")
        
        if remaining_invalid == 0:
            print("✅ 所有JSON数据已修复完成!")
        else:
            print("⚠️ 仍有无效JSON数据需要手动处理")
            
    except Exception as e:
        print(f"修复过程中出错: {e}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

def fix_json_data(invalid_json):
    """尝试修复无效的JSON数据"""
    if not invalid_json:
        return '[]'
    
    # 如果是字符串类型但不是JSON格式，尝试转换
    if isinstance(invalid_json, str):
        # 移除可能的控制字符
        cleaned = invalid_json.strip()
        
        # 如果是空字符串，返回空数组
        if not cleaned:
            return '[]'
        
        # 如果不是以[或{开头，可能是纯文本，转换为JSON格式
        if not cleaned.startswith(('[', '{')):
            # 将纯文本转换为JSON格式的问题描述
            try:
                fixed_data = [{
                    'type': '其他',
                    'description': cleaned,
                    'quantity': 1
                }]
                return json.dumps(fixed_data, ensure_ascii=False)
            except:
                return '[]'
        
        # 尝试修复常见的JSON格式问题
        try:
            # 替换单引号为双引号
            fixed = cleaned.replace("'", '"')
            # 尝试解析
            json.loads(fixed)
            return fixed
        except:
            pass
        
        try:
            # 尝试添加缺失的引号
            if cleaned.startswith('[') and cleaned.endswith(']'):
                # 简单的数组修复
                return cleaned
        except:
            pass
    
    # 如果无法修复，返回None
    return None

def test_json_extract():
    """测试JSON_EXTRACT函数是否正常工作"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n测试JSON_EXTRACT函数...")
        
        # 测试查询
        test_query = """
            SELECT COUNT(*) as count
            FROM sampling_inspection 
            WHERE defect_issues IS NOT NULL 
            AND defect_issues != '' 
            AND JSON_VALID(defect_issues) 
            AND JSON_EXTRACT(defect_issues, '$[*].description') LIKE '%测试%'
        """
        
        cursor.execute(test_query)
        result = cursor.fetchone()
        print(f"JSON_EXTRACT测试查询成功，结果: {result[0]}")
        
        return True
        
    except Exception as e:
        print(f"JSON_EXTRACT测试失败: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("=== 修复无效JSON数据工具 ===")
    print("此工具将检查并修复sampling_inspection表中的defect_issues字段")
    print("解决JSON_EXTRACT函数报错的问题\n")
    
    # 检查并修复数据
    check_and_fix_json_data()
    
    # 测试修复结果
    if test_json_extract():
        print("\n✅ 修复成功！JSON_EXTRACT函数现在可以正常工作了。")
    else:
        print("\n❌ 修复后仍有问题，请检查数据库连接和权限。")
