#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证检验日期下拉修复是否完整
检查相关的HTML、CSS和JavaScript代码
"""

import os
import re

def check_html_structure():
    """检查HTML结构是否正确"""
    print("=== 检查HTML结构 ===")
    
    file_path = "blueprints/incoming_inspection/templates/sampling_inspection.html"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键元素
    checks = [
        ('日期类型选择器ID', r'id="date-type-selector"'),
        ('检验日期选项', r'value="inspection_date".*?>检验日期'),
        ('来料日期选项', r'value="receipt_date".*?>来料日期'),
        ('日期类型下拉容器', r'class="date-type-dropdown"'),
    ]
    
    all_passed = True
    for name, pattern in checks:
        if re.search(pattern, content, re.DOTALL):
            print(f"✅ {name}: 找到")
        else:
            print(f"❌ {name}: 未找到")
            all_passed = False
    
    return all_passed

def check_css_styles():
    """检查CSS样式是否正确"""
    print("\n=== 检查CSS样式 ===")
    
    file_path = "blueprints/incoming_inspection/templates/sampling_inspection.html"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键CSS规则
    checks = [
        ('日期类型下拉样式', r'\.date-type-dropdown\s*\{'),
        ('选择器样式', r'\.date-type-dropdown select\s*\{'),
        ('强制交互样式', r'#date-type-selector\s*\{'),
        ('pointer-events设置', r'pointer-events:\s*auto\s*!important'),
        ('z-index设置', r'z-index:\s*\d+\s*!important'),
        ('cursor设置', r'cursor:\s*pointer\s*!important'),
    ]
    
    all_passed = True
    for name, pattern in checks:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ {name}: 找到")
        else:
            print(f"❌ {name}: 未找到")
            all_passed = False
    
    return all_passed

def check_javascript_logic():
    """检查JavaScript逻辑是否正确"""
    print("\n=== 检查JavaScript逻辑 ===")
    
    file_path = "blueprints/incoming_inspection/templates/sampling_inspection.html"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键JavaScript代码
    checks = [
        ('元素获取', r'getElementById\([\'"]date-type-selector[\'"]'),
        ('DOM检查包含dateTypeSelector', r'!dateTypeSelector'),
        ('change事件绑定', r'addEventListener\([\'"]change[\'"]'),
        ('click事件绑定', r'addEventListener\([\'"]click[\'"]'),
        ('强制修复函数', r'function forceFixDateTypeSelector'),
        ('样式强制设置', r'style\.pointerEvents\s*=\s*[\'"]auto[\'"]'),
        ('延迟修复调用', r'setTimeout\(forceFixDateTypeSelector'),
        ('调试日志', r'console\.log.*日期类型选择器'),
    ]
    
    all_passed = True
    for name, pattern in checks:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ {name}: 找到")
        else:
            print(f"❌ {name}: 未找到")
            all_passed = False
    
    return all_passed

def check_event_binding_logic():
    """检查事件绑定逻辑的完整性"""
    print("\n=== 检查事件绑定逻辑 ===")
    
    file_path = "blueprints/incoming_inspection/templates/sampling_inspection.html"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找事件绑定相关代码
    event_binding_pattern = r'dateTypeSelector\.addEventListener\([\'"]change[\'"].*?\}\);'
    match = re.search(event_binding_pattern, content, re.DOTALL)
    
    if match:
        print("✅ 找到change事件绑定代码")
        binding_code = match.group(0)
        
        # 检查事件处理逻辑
        if 'buildSearchUrl()' in binding_code:
            print("✅ 事件处理包含URL构建逻辑")
        else:
            print("❌ 事件处理缺少URL构建逻辑")
            return False
            
        if 'window.location.href' in binding_code:
            print("✅ 事件处理包含页面跳转逻辑")
        else:
            print("❌ 事件处理缺少页面跳转逻辑")
            return False
    else:
        print("❌ 未找到change事件绑定代码")
        return False
    
    return True

def check_debugging_features():
    """检查调试功能是否完整"""
    print("\n=== 检查调试功能 ===")
    
    file_path = "blueprints/incoming_inspection/templates/sampling_inspection.html"
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查调试相关代码
    debug_checks = [
        ('初始化日志', r'console\.log.*绑定日期类型选择器事件'),
        ('变化事件日志', r'console\.log.*日期类型选择器变化'),
        ('点击事件日志', r'console\.log.*日期类型选择器被点击'),
        ('完成日志', r'console\.log.*日期类型选择器事件绑定完成'),
        ('错误日志', r'console\.error.*日期类型选择器元素未找到'),
        ('强制修复日志', r'console\.log.*强制修复日期类型选择器完成'),
        ('缺失元素详情', r'console\.error.*缺失的元素'),
    ]
    
    all_passed = True
    for name, pattern in checks:
        if re.search(pattern, content, re.IGNORECASE):
            print(f"✅ {name}: 找到")
        else:
            print(f"❌ {name}: 未找到")
            all_passed = False
    
    return all_passed

def generate_summary():
    """生成修复总结"""
    print("\n=== 修复总结 ===")
    
    # 执行所有检查
    results = {
        'HTML结构': check_html_structure(),
        'CSS样式': check_css_styles(),
        'JavaScript逻辑': check_javascript_logic(),
        '事件绑定': check_event_binding_logic(),
        '调试功能': check_debugging_features(),
    }
    
    print(f"\n检查结果:")
    all_passed = True
    for category, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"  {category}: {status}")
        if not passed:
            all_passed = False
    
    print(f"\n总体状态: {'✅ 修复完整' if all_passed else '❌ 修复不完整'}")
    
    if all_passed:
        print("\n🎉 检验日期下拉修复已完成！")
        print("主要改进:")
        print("  - 改进了DOM元素检查逻辑")
        print("  - 添加了强制的CSS交互样式")
        print("  - 增强了JavaScript事件绑定")
        print("  - 添加了强制修复函数")
        print("  - 增加了详细的调试日志")
    else:
        print("\n⚠️ 修复可能不完整，请检查失败的项目")
    
    return all_passed

if __name__ == "__main__":
    print("=== 检验日期下拉修复验证工具 ===")
    print("检查修复是否完整和正确\n")
    
    generate_summary()
