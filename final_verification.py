#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 验证 full_inspection 功能删除是否成功
"""

import sys
import os
import requests
import time
import subprocess

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_app_startup():
    """测试应用启动"""
    print("🧪 测试应用启动...")
    
    try:
        from app import create_app
        app = create_app()
        print("✅ 应用创建成功")
        
        # 检查蓝图注册
        blueprints = list(app.blueprints.keys())
        print(f"📋 已注册的蓝图: {blueprints}")
        
        if 'full_inspection' in blueprints:
            print("❌ 发现 full_inspection 蓝图仍然存在")
            return False
        else:
            print("✅ full_inspection 蓝图已成功删除")
            
        return True
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        from db_config import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查 sampling_inspection 表是否存在
        cursor.execute("SHOW TABLES LIKE 'sampling_inspection'")
        if cursor.fetchone():
            print("✅ sampling_inspection 表存在")
        else:
            print("❌ sampling_inspection 表不存在")
            return False
        
        # 检查 full_inspection 表是否已删除
        cursor.execute("SHOW TABLES LIKE 'full_inspection'")
        if cursor.fetchone():
            print("⚠️  full_inspection 表仍然存在（需要手动删除）")
        else:
            print("✅ full_inspection 表已删除")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_routes():
    """测试路由"""
    print("\n🧪 测试路由...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.app_context():
            # 检查路由
            full_inspection_routes = []
            sampling_routes = []
            
            for rule in app.url_map.iter_rules():
                if 'full_inspection' in rule.rule:
                    full_inspection_routes.append(rule.rule)
                elif 'sampling_inspection' in rule.rule:
                    sampling_routes.append(rule.rule)
            
            if full_inspection_routes:
                print(f"❌ 发现残留的 full_inspection 路由: {full_inspection_routes}")
                return False
            else:
                print("✅ 没有发现 full_inspection 相关路由")
            
            if sampling_routes:
                print(f"✅ sampling_inspection 路由正常: {len(sampling_routes)} 个路由")
            else:
                print("⚠️  没有发现 sampling_inspection 路由")
            
            return True
            
    except Exception as e:
        print(f"❌ 路由测试失败: {e}")
        return False

def test_file_cleanup():
    """测试文件清理"""
    print("\n🧪 测试文件清理...")
    
    files_to_check = [
        'blueprints/incoming_inspection/templates/full_inspection.html',
        'blueprints/incoming_inspection/templates/new_full_inspection.html'
    ]
    
    remaining_files = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            remaining_files.append(file_path)
    
    if remaining_files:
        print(f"❌ 发现残留文件: {remaining_files}")
        return False
    else:
        print("✅ 相关模板文件已清理完成")
        return True

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        # 测试主要模块导入
        from blueprints.incoming_inspection import incoming_inspection_bp, sampling_inspection_bp
        print("✅ 蓝图导入成功")
        
        # 测试工具模块
        sys.path.append('utils')
        from report_code_generator import generate_report_code
        
        # 测试报告编码生成（只支持抽样检验）
        from datetime import datetime
        code = generate_report_code('sampling', datetime.now())
        print(f"✅ 报告编码生成成功: {code}")
        
        # 测试不支持的类型
        try:
            generate_report_code('full', datetime.now())
            print("❌ 仍然支持 full 类型的报告编码生成")
            return False
        except (ValueError, Exception) as e:
            if "不支持的检验类型" in str(e):
                print("✅ 正确拒绝了 full 类型的报告编码生成")
            else:
                print(f"⚠️  报告编码生成失败，但原因不明: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终验证 - full_inspection 功能删除")
    print("=" * 60)
    
    tests = [
        ("应用启动测试", test_app_startup),
        ("数据库连接测试", test_database_connection),
        ("路由测试", test_routes),
        ("文件清理测试", test_file_cleanup),
        ("导入测试", test_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！full_inspection 功能已成功删除")
        print("\n📝 总结:")
        print("  ✅ 后端代码已清理")
        print("  ✅ 前端模板已删除")
        print("  ✅ 路由已清理")
        print("  ✅ 导入已修复")
        print("  ✅ 应用可以正常启动")
        print("\n🔧 后续步骤:")
        print("  1. 如果数据库中还有 full_inspection 表，可运行删除脚本")
        print("  2. 重启应用服务器")
        print("  3. 测试抽样检验功能")
        return True
    else:
        print("❌ 部分测试失败，请检查上述错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
