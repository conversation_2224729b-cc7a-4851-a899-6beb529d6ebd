#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证JSON_EXTRACT错误修复
模拟原始错误的HTTP请求
"""

import requests
import json
from urllib.parse import urlencode

def test_original_error_url():
    """测试原始错误的URL"""
    
    # 原始错误的URL参数
    params = {
        'start_date': '2025-07-26',
        'end_date': '2025-08-02',
        'date_type': 'inspection_date',
        'quick_search': 'ABC001',
        'material_number': '',
        'supplier': '',
        'purchase_order': '',
        'defect_rate_op': 'gt',
        'defect_rate': '',
        'total_quantity_op': 'gt',
        'total_quantity': '',
        'inspection_result': '',
        'receipt_start_date': '2025-07-19',
        'receipt_end_date': '2025-08-02',
        'page': '1'
    }
    
    # 构建URL
    base_url = 'http://127.0.0.1:5000/sampling_inspection/'
    url = f"{base_url}?{urlencode(params)}"
    
    print("=== 验证JSON_EXTRACT错误修复 ===")
    print(f"测试URL: {url}")
    print()
    
    try:
        # 发送请求
        print("发送HTTP请求...")
        response = requests.get(url, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功！页面正常加载")
            return True
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：请确保Flask应用正在运行 (python app.py)")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    test_original_error_url()
