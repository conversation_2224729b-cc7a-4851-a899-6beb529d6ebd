<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽样检验记录筛选功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .test-info h2 {
            color: #1976d2;
            margin-top: 0;
        }
        
        /* 复制筛选区域样式 */
        .filter-section {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 1px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
            margin-bottom: 10px;
            justify-content: flex-start;
        }
        
        .filter-row:last-child {
            margin-bottom: 0;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: auto;
            flex: none;
        }
        
        .filter-control {
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            outline: none;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .filter-control:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
        }
        
        .filter-control::placeholder {
            color: #6c757d;
            opacity: 1;
        }
        
        .filter-number-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .filter-operator {
            width: 60px;
            height: 32px;
            padding: 4px 6px;
            font-size: 11px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
        }
        
        .filter-number {
            width: 80px;
            height: 32px;
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            background-color: white;
            color: #495057;
            text-align: right;
        }
        
        .filter-actions {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            margin-left: auto;
        }
        
        .filter-btn {
            height: 32px;
            padding: 6px 12px;
            font-size: 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.15s ease-in-out;
            white-space: nowrap;
        }
        
        .filter-btn-primary {
            background-color: #1976d2;
            color: white;
        }
        
        .filter-btn-primary:hover {
            background-color: #1565c0;
        }
        
        .filter-btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .filter-btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .feature-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .feature-list h3 {
            color: #333;
            margin-top: 0;
        }
        
        .log-output {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            margin-top: 20px;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .filter-row {
                flex-direction: column;
                gap: 10px;
            }
            
            .filter-group {
                min-width: 100%;
            }
            
            .filter-actions {
                margin-left: 0;
                justify-content: center;
                width: 100%;
            }
            
            .filter-number-group {
                justify-content: space-between;
            }
            
            .filter-operator {
                width: 80px;
            }
            
            .filter-number {
                flex: 1;
                min-width: 100px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h2>抽样检验记录筛选功能测试</h2>
            <p>此页面用于测试新增的筛选功能，包括报告编号、供应商、检验类型、不良率和检验结果的筛选。</p>
        </div>
        
        <div class="feature-list">
            <h3>新增功能特性</h3>
            <ul>
                <li><strong>报告编号筛选</strong>：支持模糊搜索，可输入部分编号进行匹配</li>
                <li><strong>供应商筛选</strong>：下拉选择，显示所有已有供应商</li>
                <li><strong>检验类型筛选</strong>：下拉选择，包含抽样检验、全部检验、免检（显示中文，提交英文值）</li>
                <li><strong>不良率筛选</strong>：支持大于、大于等于、小于、小于等于、等于五种比较方式</li>
                <li><strong>检验结果筛选</strong>：下拉选择，包含合格、不合格（基于不良数量计算）</li>
                <li><strong>组合筛选</strong>：所有筛选条件采用"与"的关联形式</li>
                <li><strong>重置功能</strong>：一键清空所有筛选条件</li>
                <li><strong>状态保持</strong>：筛选后分页和刷新页面时保持筛选状态</li>
                <li><strong>翻译显示</strong>：检验类型显示中文，与表格显示保持一致</li>
            </ul>
        </div>
        
        <!-- 筛选区域演示 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-group">
                    <input type="text" class="filter-control" id="filter-report-code" placeholder="报告编号">
                </div>

                <div class="filter-group">
                    <select class="filter-control" id="filter-supplier">
                        <option value="">供应商</option>
                        <!-- 实际应用中，这些选项来自数据库查询 -->
                        <option value="深圳市科技有限公司">深圳市科技有限公司</option>
                        <option value="广州电子制造厂">广州电子制造厂</option>
                        <option value="东莞精密器件厂">东莞精密器件厂</option>
                        <option value="苏州半导体公司">苏州半导体公司</option>
                    </select>
                </div>

                <div class="filter-group">
                    <select class="filter-control" id="filter-inspection-type">
                        <option value="">检验类型</option>
                        <!-- 实际应用中，这些选项来自数据库查询，值为英文，显示为中文 -->
                        <option value="sampling">抽样检验</option>
                        <option value="full">全部检验</option>
                        <option value="exempt">免检</option>
                    </select>
                </div>

                <div class="filter-group">
                    <div class="filter-number-group">
                        <select class="filter-operator" id="filter-defect-rate-op" title="不良率比较方式">
                            <option value="gt">大于</option>
                            <option value="gte">大于等于</option>
                            <option value="lt">小于</option>
                            <option value="lte">小于等于</option>
                            <option value="eq">等于</option>
                        </select>
                        <input type="number" class="filter-number" id="filter-defect-rate" placeholder="不良率%" step="0.01" min="0" max="100">
                    </div>
                </div>

                <div class="filter-group">
                    <select class="filter-control" id="filter-inspection-result">
                        <option value="">检验结果</option>
                        <!-- 实际应用中，这些选项来自数据库查询 -->
                        <option value="合格">合格</option>
                        <option value="不合格">不合格</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button type="button" class="filter-btn filter-btn-primary" id="apply-filters">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <button type="button" class="filter-btn filter-btn-secondary" id="reset-filters">
                        <i class="fas fa-undo"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        
        <div class="feature-list">
            <h3>使用说明</h3>
            <ol>
                <li><strong>单独筛选</strong>：可以只使用其中一个或几个筛选条件</li>
                <li><strong>组合筛选</strong>：多个条件同时使用时，采用"与"的关系（同时满足所有条件）</li>
                <li><strong>模糊匹配</strong>：报告编号支持模糊搜索，输入部分内容即可匹配</li>
                <li><strong>精确匹配</strong>：供应商、检验类型、检验结果采用精确匹配</li>
                <li><strong>数值比较</strong>：不良率支持多种比较方式，输入数值时请注意单位为百分比</li>
                <li><strong>翻译显示</strong>：检验类型显示中文（抽样检验、全部检验、免检），但提交时使用英文值</li>
                <li><strong>计算字段</strong>：检验结果基于不良数量计算（不良数量=0为合格，>0为不合格）</li>
                <li><strong>重置功能</strong>：点击重置按钮可清空所有筛选条件并刷新页面</li>
                <li><strong>回车搜索</strong>：在报告编号和不良率输入框中按回车键可直接执行筛选</li>
            </ol>
        </div>
        
        <div id="log-output" class="log-output"></div>
    </div>

    <script>
        let logOutput = document.getElementById('log-output');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        // 初始化筛选功能演示
        document.addEventListener('DOMContentLoaded', function() {
            log('筛选功能演示页面加载完成');
            
            // 获取筛选元素
            const filterElements = {
                reportCode: document.getElementById('filter-report-code'),
                supplier: document.getElementById('filter-supplier'),
                inspectionType: document.getElementById('filter-inspection-type'),
                defectRateOp: document.getElementById('filter-defect-rate-op'),
                defectRate: document.getElementById('filter-defect-rate'),
                inspectionResult: document.getElementById('filter-inspection-result'),
                applyBtn: document.getElementById('apply-filters'),
                resetBtn: document.getElementById('reset-filters')
            };
            
            // 绑定筛选按钮事件
            filterElements.applyBtn.addEventListener('click', function() {
                log('点击筛选按钮');
                applyFilters(filterElements);
            });
            
            // 绑定重置按钮事件
            filterElements.resetBtn.addEventListener('click', function() {
                log('点击重置按钮');
                resetFilters(filterElements);
            });
            
            // 绑定回车键事件
            [filterElements.reportCode, filterElements.defectRate].forEach(input => {
                if (input) {
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            log('按下回车键，执行筛选');
                            applyFilters(filterElements);
                        }
                    });
                }
            });
            
            // 绑定值变化事件
            Object.entries(filterElements).forEach(([name, element]) => {
                if (element && name !== 'applyBtn' && name !== 'resetBtn') {
                    element.addEventListener('change', function() {
                        log(`${name} 值变化为: ${this.value}`);
                    });
                }
            });
            
            log('筛选功能事件绑定完成');
        });
        
        // 应用筛选（演示）
        function applyFilters(elements) {
            const filters = {
                reportCode: elements.reportCode.value.trim(),
                supplier: elements.supplier.value,
                inspectionType: elements.inspectionType.value,
                defectRateOp: elements.defectRateOp.value,
                defectRate: elements.defectRate.value,
                inspectionResult: elements.inspectionResult.value
            };
            
            log('应用筛选条件:');
            Object.entries(filters).forEach(([key, value]) => {
                if (value) {
                    log(`  ${key}: ${value}`);
                }
            });
            
            // 构建URL参数（演示）
            const params = new URLSearchParams();
            if (filters.reportCode) params.set('report_code', filters.reportCode);
            if (filters.supplier) params.set('supplier', filters.supplier);
            if (filters.inspectionType) params.set('inspection_type', filters.inspectionType);
            if (filters.defectRate) {
                params.set('defect_rate_op', filters.defectRateOp);
                params.set('defect_rate', filters.defectRate);
            }
            if (filters.inspectionResult) params.set('inspection_result', filters.inspectionResult);
            
            const queryString = params.toString();
            log(`生成的查询参数: ${queryString || '(无)'}`);
            
            if (queryString) {
                log(`完整URL: /sampling_inspection/?${queryString}`);
            } else {
                log('没有筛选条件，将显示所有记录');
            }
        }
        
        // 重置筛选（演示）
        function resetFilters(elements) {
            elements.reportCode.value = '';
            elements.supplier.value = '';
            elements.inspectionType.value = '';
            elements.defectRateOp.value = 'gt';
            elements.defectRate.value = '';
            elements.inspectionResult.value = '';
            
            log('所有筛选条件已重置');
            log('将跳转到: /sampling_inspection/');
        }
    </script>
</body>
</html>
