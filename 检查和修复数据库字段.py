#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复数据库字段脚本
检查sampling_inspection表是否缺少必要的字段，如果缺少则添加
"""

import mysql.connector
from config import DB_CONFIG

def get_db_connection():
    """获取数据库连接"""
    return mysql.connector.connect(**DB_CONFIG)

def check_and_add_missing_fields():
    """检查并添加缺失的字段"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        print("=== 检查sampling_inspection表结构 ===")
        
        # 获取当前表结构
        cursor.execute("SHOW COLUMNS FROM sampling_inspection")
        existing_columns = {row['Field']: row for row in cursor.fetchall()}
        
        print(f"当前表中的字段: {list(existing_columns.keys())}")
        
        # 需要的字段定义
        required_fields = {
            'inspection_type': {
                'type': 'VARCHAR(20)',
                'default': "'sampling'",
                'comment': '检验类型：sampling-抽样检验，full-全部检验，exempt-免检'
            },
            'batch_number': {
                'type': 'VARCHAR(100)',
                'default': 'NULL',
                'comment': '批次号'
            }
        }
        
        # 检查并添加缺失的字段
        for field_name, field_info in required_fields.items():
            if field_name not in existing_columns:
                print(f"❌ 缺少字段: {field_name}")
                
                # 添加字段
                alter_sql = f"""
                    ALTER TABLE sampling_inspection 
                    ADD COLUMN {field_name} {field_info['type']} 
                    DEFAULT {field_info['default']} 
                    COMMENT '{field_info['comment']}'
                """
                
                try:
                    cursor.execute(alter_sql)
                    print(f"✅ 已添加字段: {field_name}")
                except Exception as e:
                    print(f"❌ 添加字段 {field_name} 失败: {e}")
            else:
                print(f"✅ 字段存在: {field_name}")
        
        # 检查inspection_type字段的数据
        print("\n=== 检查inspection_type字段数据 ===")
        try:
            cursor.execute("SELECT DISTINCT inspection_type, COUNT(*) as count FROM sampling_inspection GROUP BY inspection_type")
            type_data = cursor.fetchall()
            
            if type_data:
                print("检验类型分布:")
                for row in type_data:
                    print(f"  {row['inspection_type'] or 'NULL'}: {row['count']} 条记录")
            else:
                print("表中暂无数据")
                
            # 如果有NULL值，更新为默认值
            cursor.execute("UPDATE sampling_inspection SET inspection_type = 'sampling' WHERE inspection_type IS NULL OR inspection_type = ''")
            updated_rows = cursor.rowcount
            if updated_rows > 0:
                print(f"✅ 已将 {updated_rows} 条记录的inspection_type更新为'sampling'")
                
        except Exception as e:
            print(f"❌ 检查inspection_type数据失败: {e}")
        
        # 提交更改
        conn.commit()
        print("\n=== 数据库字段检查和修复完成 ===")
        
        # 最终验证
        print("\n=== 最终验证 ===")
        cursor.execute("SHOW COLUMNS FROM sampling_inspection")
        final_columns = [row['Field'] for row in cursor.fetchall()]
        print(f"最终表结构: {final_columns}")
        
        # 验证筛选查询
        print("\n=== 验证筛选查询 ===")
        try:
            cursor.execute("SELECT DISTINCT supplier FROM sampling_inspection WHERE supplier IS NOT NULL AND supplier != '' ORDER BY supplier LIMIT 5")
            suppliers = [row['supplier'] for row in cursor.fetchall()]
            print(f"✅ 供应商查询成功，示例: {suppliers[:3]}")
        except Exception as e:
            print(f"❌ 供应商查询失败: {e}")
            
        try:
            cursor.execute("SELECT DISTINCT inspection_type FROM sampling_inspection WHERE inspection_type IS NOT NULL AND inspection_type != '' ORDER BY inspection_type")
            types = [row['inspection_type'] for row in cursor.fetchall()]
            print(f"✅ 检验类型查询成功: {types}")
        except Exception as e:
            print(f"❌ 检验类型查询失败: {e}")
            
        try:
            # 测试检验结果计算
            cursor.execute("SELECT COUNT(*) as total, SUM(CASE WHEN defect_quantity = 0 THEN 1 ELSE 0 END) as qualified FROM sampling_inspection")
            result = cursor.fetchone()
            print(f"✅ 检验结果计算成功: 总记录 {result['total']}, 合格 {result['qualified']}")
        except Exception as e:
            print(f"❌ 检验结果计算失败: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接或操作失败: {e}")

def create_indexes():
    """创建筛选功能需要的索引"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("\n=== 创建筛选功能索引 ===")
        
        indexes = [
            ("idx_sampling_supplier", "sampling_inspection", "supplier"),
            ("idx_sampling_inspection_type", "sampling_inspection", "inspection_type"),
            ("idx_sampling_report_code", "sampling_inspection", "report_code"),
            ("idx_sampling_defect_quantity", "sampling_inspection", "defect_quantity"),
        ]
        
        for index_name, table_name, column_name in indexes:
            try:
                # 检查索引是否已存在
                cursor.execute(f"SHOW INDEX FROM {table_name} WHERE Key_name = '{index_name}'")
                if cursor.fetchone():
                    print(f"✅ 索引已存在: {index_name}")
                else:
                    # 创建索引
                    cursor.execute(f"CREATE INDEX {index_name} ON {table_name}({column_name})")
                    print(f"✅ 已创建索引: {index_name}")
            except Exception as e:
                print(f"❌ 创建索引 {index_name} 失败: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")

def main():
    """主函数"""
    print("开始检查和修复数据库字段...")
    
    # 检查和添加缺失字段
    check_and_add_missing_fields()
    
    # 创建索引
    create_indexes()
    
    print("\n🎉 数据库字段检查和修复完成！")
    print("\n建议:")
    print("1. 重启应用程序以确保更改生效")
    print("2. 测试筛选功能是否正常工作")
    print("3. 如果仍有问题，请检查应用程序日志")

if __name__ == "__main__":
    main()
