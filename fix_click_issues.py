#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复页面上半部分按钮无法点击的问题
主要修复CSS中的z-index、定位和覆盖层问题
"""

import os
import re
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已备份: {file_path} -> {backup_path}")
        return backup_path
    return None

def fix_css_issues():
    """修复CSS中的点击问题"""
    css_file = "static/css/style.css"
    
    if not os.path.exists(css_file):
        print(f"❌ CSS文件不存在: {css_file}")
        return False
    
    # 备份CSS文件
    backup_file(css_file)
    
    with open(css_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 降低浮动面板的z-index
    content = re.sub(
        r'\.floating-panel\s*{[^}]*z-index:\s*\d+;',
        lambda m: m.group(0).replace(re.search(r'z-index:\s*\d+', m.group(0)).group(0), 'z-index: 100'),
        content
    )
    
    # 修复2: 确保浮动面板初始状态不影响其他元素
    floating_panel_fix = """
/* 浮动搜索面板 - 修复点击问题 */
.floating-panel {
    position: fixed;
    top: 0;
    left: -450px; /* 初始隐藏在左侧 */
    width: 400px;
    height: 100vh;
    background-color: white;
    border-radius: 0 8px 8px 0;
    box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
    z-index: 100; /* 降低z-index */
    transition: left 0.35s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
    pointer-events: none; /* 隐藏时不阻止点击 */
}

.floating-panel.active {
    left: 0;
    pointer-events: auto; /* 显示时恢复点击 */
}"""
    
    # 替换浮动面板样式
    content = re.sub(
        r'\/\* 浮动搜索面板 \*\/.*?(?=\/\*|$)',
        floating_panel_fix,
        content,
        flags=re.DOTALL
    )
    
    # 修复3: 降低下拉菜单的z-index
    content = re.sub(
        r'(\.dropdown-menu\s*{[^}]*?)z-index:\s*\d+;',
        r'\1z-index: 50;',
        content
    )
    
    # 修复4: 确保模态框不会意外阻止点击
    modal_fix = """
/* 模态框样式 - 修复点击问题 */
.modal {
    display: none;
    position: fixed;
    z-index: 200; /* 适中的z-index */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    pointer-events: none; /* 隐藏时不阻止点击 */
}

.modal.show {
    pointer-events: auto; /* 显示时恢复点击 */
}"""
    
    # 替换模态框样式
    content = re.sub(
        r'\/\* 模态框样式 \*\/.*?(?=\/\*|\.modal-content)',
        modal_fix,
        content,
        flags=re.DOTALL
    )
    
    # 修复5: 移动端下拉菜单z-index问题
    content = re.sub(
        r'z-index:\s*2000;',
        'z-index: 150;',
        content
    )
    
    # 修复6: 确保按钮有合适的z-index
    button_fix = """
/* 按钮点击修复 */
.btn, .test-button, button {
    position: relative;
    z-index: 10;
    pointer-events: auto;
}

.nav-link, .dropdown-item, .menu-item {
    position: relative;
    z-index: 10;
    pointer-events: auto;
}"""
    
    content += "\n" + button_fix
    
    # 写入修复后的内容
    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ CSS修复完成: {css_file}")
    return True

def fix_javascript_issues():
    """修复JavaScript中的事件处理问题"""
    js_file = "static/js/main.js"
    
    if not os.path.exists(js_file):
        print(f"❌ JavaScript文件不存在: {js_file}")
        return False
    
    # 备份JavaScript文件
    backup_file(js_file)
    
    with open(js_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 确保浮动面板初始状态正确
    panel_init_fix = """
// 修复浮动面板初始状态
function initFloatingPanel() {
    const floatingPanel = document.getElementById('floating-search-panel');
    if (floatingPanel) {
        // 确保初始状态不阻止点击
        floatingPanel.style.pointerEvents = 'none';
        floatingPanel.classList.remove('active');
        
        // 只有在激活时才允许点击
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (floatingPanel.classList.contains('active')) {
                        floatingPanel.style.pointerEvents = 'auto';
                    } else {
                        floatingPanel.style.pointerEvents = 'none';
                    }
                }
            });
        });
        
        observer.observe(floatingPanel, { attributes: true });
    }
}"""
    
    # 替换或添加浮动面板初始化函数
    if 'function initFloatingPanel()' in content:
        content = re.sub(
            r'function initFloatingPanel\(\)\s*{[^}]*}',
            panel_init_fix.strip(),
            content,
            flags=re.DOTALL
        )
    else:
        content += "\n" + panel_init_fix
    
    # 修复2: 确保点击事件不会被意外阻止
    click_fix = """
// 修复点击事件处理
function fixClickEvents() {
    // 确保所有按钮和链接可以正常点击
    document.querySelectorAll('button, .btn, a, .nav-link, .dropdown-item').forEach(element => {
        element.style.pointerEvents = 'auto';
        element.style.position = 'relative';
        element.style.zIndex = '10';
    });
    
    // 移除可能阻止点击的覆盖层
    document.querySelectorAll('*').forEach(element => {
        const style = window.getComputedStyle(element);
        if (style.position === 'fixed' || style.position === 'absolute') {
            const zIndex = parseInt(style.zIndex);
            if (zIndex > 1000 && !element.classList.contains('modal')) {
                element.style.zIndex = '100';
            }
        }
    });
}

// 页面加载时执行修复
document.addEventListener('DOMContentLoaded', function() {
    fixClickEvents();
});"""
    
    content += "\n" + click_fix
    
    # 写入修复后的内容
    with open(js_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ JavaScript修复完成: {js_file}")
    return True

def create_test_page():
    """创建测试页面来验证修复效果"""
    test_content = """{% extends "base.html" %}

{% block title %}点击测试页面{% endblock %}

{% block content %}
<div class="page-header">
    <h1>点击功能测试页面</h1>
</div>

<div class="row">
    <div class="col">
        <div class="card">
            <h2>页面上半部分测试</h2>
            <p>这些按钮应该都可以正常点击：</p>
            
            <button class="btn btn-primary" onclick="alert('按钮1点击成功！')">测试按钮1</button>
            <button class="btn btn-success" onclick="alert('按钮2点击成功！')">测试按钮2</button>
            <button class="btn btn-warning" onclick="alert('按钮3点击成功！')">测试按钮3</button>
            
            <br><br>
            
            <a href="#" class="btn btn-info" onclick="alert('链接按钮点击成功！'); return false;">链接按钮测试</a>
            
            <br><br>
            
            <div class="form-group">
                <label>输入框测试：</label>
                <input type="text" class="form-control" placeholder="这个输入框应该可以正常输入">
            </div>
            
            <div class="form-group">
                <label>下拉框测试：</label>
                <select class="form-control">
                    <option>选项1</option>
                    <option>选项2</option>
                    <option>选项3</option>
                </select>
            </div>
        </div>
    </div>
</div>

<script>
// 测试脚本
document.addEventListener('DOMContentLoaded', function() {
    console.log('测试页面加载完成');
    
    // 检查是否有元素阻止点击
    const problematicElements = [];
    document.querySelectorAll('*').forEach(el => {
        const style = window.getComputedStyle(el);
        const rect = el.getBoundingClientRect();
        
        if (rect.top < 300 && (style.position === 'fixed' || style.position === 'absolute')) {
            const zIndex = parseInt(style.zIndex) || 0;
            if (zIndex > 50) {
                problematicElements.push({
                    element: el.tagName + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                    zIndex: zIndex,
                    position: style.position
                });
            }
        }
    });
    
    if (problematicElements.length > 0) {
        console.warn('发现可能影响点击的元素:', problematicElements);
    } else {
        console.log('✅ 未发现影响点击的元素');
    }
});
</script>
{% endblock %}"""
    
    test_file = "templates/click_test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 测试页面创建完成: {test_file}")
    return True

def main():
    """主函数"""
    print("🔧 开始修复页面点击问题...")
    print("=" * 50)
    
    success_count = 0
    total_fixes = 3
    
    # 修复CSS问题
    if fix_css_issues():
        success_count += 1
    
    # 修复JavaScript问题
    if fix_javascript_issues():
        success_count += 1
    
    # 创建测试页面
    if create_test_page():
        success_count += 1
    
    print("=" * 50)
    print(f"🎯 修复完成: {success_count}/{total_fixes} 项成功")
    
    if success_count == total_fixes:
        print("✅ 所有修复都已完成！")
        print("\n📋 后续步骤:")
        print("1. 重启Flask应用")
        print("2. 访问 /click_test 页面测试点击功能")
        print("3. 检查浏览器控制台是否有错误信息")
        print("4. 测试导航菜单和按钮是否正常工作")
    else:
        print("⚠️  部分修复失败，请检查错误信息")
    
    return success_count == total_fixes

if __name__ == "__main__":
    main()
