{% extends "base.html" %}

{% block title %}点击问题诊断{% endblock %}

{% block extra_css %}
<style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .diagnostic-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #1565c0;
        }
        
        .overlay-test {
            position: relative;
        }
        
        .overlay-test::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 0, 0, 0.1);
            z-index: 10;
            pointer-events: none;
        }
        
        .high-z-index {
            position: relative;
            z-index: 1000;
        }
        
        .fixed-position {
            position: fixed;
            top: 100px;
            left: 50px;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
        }
        
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            font-weight: bold;
        }
        
        .error {
            background: #ffeaea;
            border-left-color: #f44336;
            color: #d32f2f;
        }
        
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="diagnostic-container">
        <h1>页面点击问题诊断工具</h1>
        
        <div class="info-box">
            <strong>问题描述：</strong> 页面上半部分的按钮无法选择或点击
            <br><strong>可能原因：</strong> CSS覆盖层、z-index问题、JavaScript事件阻止、定位问题等
        </div>
        
        <!-- 测试1: 基础按钮测试 -->
        <div class="test-section">
            <h3>测试1: 基础按钮功能</h3>
            <button class="test-button" onclick="showResult('test1', '基础按钮点击正常')">
                基础测试按钮
            </button>
            <div id="test1-result"></div>
        </div>
        
        <!-- 测试2: 覆盖层测试 -->
        <div class="test-section overlay-test">
            <h3>测试2: 覆盖层影响测试</h3>
            <p>这个区域有一个透明覆盖层（红色半透明），但设置了 pointer-events: none</p>
            <button class="test-button" onclick="showResult('test2', '覆盖层测试通过 - pointer-events: none 生效')">
                覆盖层下的按钮
            </button>
            <div id="test2-result"></div>
        </div>
        
        <!-- 测试3: Z-index测试 -->
        <div class="test-section">
            <h3>测试3: Z-index层级测试</h3>
            <button class="test-button high-z-index" onclick="showResult('test3', 'Z-index 1000 按钮点击正常')">
                高Z-index按钮 (z-index: 1000)
            </button>
            <div id="test3-result"></div>
        </div>
        
        <!-- 测试4: Fixed定位测试 -->
        <div class="test-section">
            <h3>测试4: Fixed定位测试</h3>
            <div class="fixed-position">
                <button class="test-button" onclick="showResult('test4', 'Fixed定位按钮点击正常')">
                    Fixed定位按钮
                </button>
                <button class="test-button" onclick="closeFixed()">关闭</button>
            </div>
            <button class="test-button" onclick="document.querySelector('.fixed-position').style.display='block'">
                显示Fixed定位测试
            </button>
            <div id="test4-result"></div>
        </div>
        
        <!-- 测试5: 事件传播测试 -->
        <div class="test-section" onclick="showResult('test5', '父元素点击事件触发', 'error')">
            <h3>测试5: 事件传播测试</h3>
            <p>点击按钮应该只触发按钮事件，不触发父元素事件</p>
            <button class="test-button" onclick="event.stopPropagation(); showResult('test5', '按钮点击正常，事件传播已阻止')">
                阻止事件传播的按钮
            </button>
            <div id="test5-result"></div>
        </div>
        
        <!-- 诊断信息 -->
        <div class="test-section">
            <h3>浏览器诊断信息</h3>
            <div class="code-block" id="browser-info"></div>
            <button class="test-button" onclick="collectBrowserInfo()">收集浏览器信息</button>
        </div>
        
        <!-- CSS检查 -->
        <div class="test-section">
            <h3>CSS样式检查</h3>
            <button class="test-button" onclick="checkCSSIssues()">检查常见CSS问题</button>
            <div id="css-check-result"></div>
        </div>
        
        <!-- 解决方案建议 -->
        <div class="test-section">
            <h3>常见解决方案</h3>
            <div class="info-box">
                <strong>1. 检查CSS覆盖层：</strong>
                <div class="code-block">/* 确保覆盖层不阻止点击 */
.overlay {
    pointer-events: none;
}</div>
                
                <strong>2. 检查Z-index层级：</strong>
                <div class="code-block">/* 确保按钮在最上层 */
.button {
    position: relative;
    z-index: 999;
}</div>
                
                <strong>3. 检查JavaScript事件：</strong>
                <div class="code-block">/* 避免阻止默认行为 */
element.addEventListener('click', function(e) {
    // 不要无条件使用 e.preventDefault()
});</div>
                
                <strong>4. 检查定位问题：</strong>
                <div class="code-block">/* 检查是否有元素意外覆盖 */
.container {
    position: relative;
    overflow: visible;
}</div>
            </div>
        </div>
    </div>
    
    <script>
        function showResult(testId, message, type = 'success') {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = `<div class="result ${type === 'error' ? 'error' : ''}">${message}</div>`;
        }
        
        function closeFixed() {
            document.querySelector('.fixed-position').style.display = 'none';
            showResult('test4', 'Fixed定位元素已关闭');
        }
        
        function collectBrowserInfo() {
            const info = {
                userAgent: navigator.userAgent,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                devicePixelRatio: window.devicePixelRatio,
                touchSupport: 'ontouchstart' in window,
                scrollPosition: `${window.scrollX}, ${window.scrollY}`
            };
            
            document.getElementById('browser-info').textContent = JSON.stringify(info, null, 2);
        }
        
        function checkCSSIssues() {
            const issues = [];
            
            // 检查是否有高z-index的元素可能覆盖按钮
            const allElements = document.querySelectorAll('*');
            const highZIndexElements = [];
            
            allElements.forEach(el => {
                const style = window.getComputedStyle(el);
                const zIndex = parseInt(style.zIndex);
                if (zIndex > 100) {
                    highZIndexElements.push({
                        element: el.tagName + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                        zIndex: zIndex,
                        position: style.position
                    });
                }
            });
            
            if (highZIndexElements.length > 0) {
                issues.push('发现高z-index元素：' + JSON.stringify(highZIndexElements, null, 2));
            }
            
            // 检查是否有fixed或absolute定位的元素
            const positionedElements = [];
            allElements.forEach(el => {
                const style = window.getComputedStyle(el);
                if (style.position === 'fixed' || style.position === 'absolute') {
                    const rect = el.getBoundingClientRect();
                    if (rect.top < 200) { // 页面上半部分
                        positionedElements.push({
                            element: el.tagName + (el.className ? '.' + el.className.split(' ').join('.') : ''),
                            position: style.position,
                            top: rect.top,
                            left: rect.left,
                            width: rect.width,
                            height: rect.height
                        });
                    }
                }
            });
            
            if (positionedElements.length > 0) {
                issues.push('页面上半部分的定位元素：' + JSON.stringify(positionedElements, null, 2));
            }
            
            const resultDiv = document.getElementById('css-check-result');
            if (issues.length > 0) {
                resultDiv.innerHTML = `<div class="result error">发现潜在问题：<pre>${issues.join('\n\n')}</pre></div>`;
            } else {
                resultDiv.innerHTML = `<div class="result">未发现明显的CSS问题</div>`;
            }
        }
        
        // 页面加载时自动收集信息
        window.addEventListener('load', function() {
            collectBrowserInfo();
            
            // 添加点击监听器来检测点击事件
            document.addEventListener('click', function(e) {
                console.log('点击事件:', {
                    target: e.target.tagName + (e.target.className ? '.' + e.target.className : ''),
                    clientX: e.clientX,
                    clientY: e.clientY,
                    pageX: e.pageX,
                    pageY: e.pageY
                });
            });
        });
    </script>
{% endblock %}
