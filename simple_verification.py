#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化验证脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """主验证函数"""
    print("🚀 验证 full_inspection 功能删除")
    print("=" * 40)
    
    success_count = 0
    total_tests = 4
    
    # 测试1: 应用导入
    print("\n1. 测试应用导入...")
    try:
        from app import create_app
        app = create_app()
        blueprints = list(app.blueprints.keys())
        
        if 'full_inspection' in blueprints:
            print("❌ full_inspection 蓝图仍然存在")
        else:
            print("✅ full_inspection 蓝图已删除")
            success_count += 1
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
    
    # 测试2: 蓝图导入
    print("\n2. 测试蓝图导入...")
    try:
        from blueprints.incoming_inspection import incoming_inspection_bp, sampling_inspection_bp
        print("✅ 蓝图导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 蓝图导入失败: {e}")
    
    # 测试3: 文件清理
    print("\n3. 测试文件清理...")
    files_to_check = [
        'blueprints/incoming_inspection/templates/full_inspection.html',
        'blueprints/incoming_inspection/templates/new_full_inspection.html'
    ]
    
    remaining_files = [f for f in files_to_check if os.path.exists(f)]
    
    if remaining_files:
        print(f"❌ 发现残留文件: {remaining_files}")
    else:
        print("✅ 模板文件已清理")
        success_count += 1
    
    # 测试4: 数据库连接
    print("\n4. 测试数据库...")
    try:
        from db_config import get_db_connection
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查 sampling_inspection 表
        cursor.execute("SHOW TABLES LIKE 'sampling_inspection'")
        if cursor.fetchone():
            print("✅ sampling_inspection 表存在")
            success_count += 1
        else:
            print("❌ sampling_inspection 表不存在")
        
        # 检查 full_inspection 表
        cursor.execute("SHOW TABLES LIKE 'full_inspection'")
        if cursor.fetchone():
            print("⚠️  full_inspection 表仍存在（可手动删除）")
        else:
            print("✅ full_inspection 表已删除")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
    
    # 结果
    print("\n" + "=" * 40)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count >= 3:
        print("🎉 full_inspection 功能删除基本成功！")
        print("\n📝 系统状态:")
        print("  ✅ 应用可以正常启动")
        print("  ✅ 只保留抽样检验功能")
        print("  ✅ 前端模板已清理")
        print("  ✅ 后端代码已修改")
        
        print("\n🔧 建议后续步骤:")
        print("  1. 启动应用: python app.py")
        print("  2. 访问: http://127.0.0.1:5000")
        print("  3. 测试抽样检验功能")
        print("  4. 如需要，删除 full_inspection 数据库表")
        
        return True
    else:
        print("❌ 删除不完整，请检查错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
