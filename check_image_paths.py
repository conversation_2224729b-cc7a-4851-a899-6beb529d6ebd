#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的图片路径
"""

from db_config import get_db_connection
import os

def check_image_paths():
    try:
        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)
        
        # 查询一些有图片的问题点记录
        cursor.execute("""
            SELECT msq.question_number, msq.question_text, msq.image_path, mscf.report_code
            FROM material_sample_questions msq
            JOIN material_sample_confirmation_form mscf ON msq.form_id = mscf.id
            WHERE msq.image_path IS NOT NULL AND msq.image_path != ''
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        print('=== 数据库中的图片路径示例 ===')
        for record in records:
            print(f'报告编码: {record["report_code"]}')
            print(f'问题点: {record["question_number"]}')
            print(f'图片路径: {record["image_path"]}')
            
            # 检查文件是否存在
            image_path = record["image_path"]
            if image_path:
                exists = os.path.exists(image_path)
                print(f'文件存在: {exists}')
                
                # 尝试转换为Web路径
                if os.path.isabs(image_path):
                    # 查找static目录
                    static_dir = os.path.join(os.path.dirname(__file__), 'static')
                    print(f'Static目录: {static_dir}')
                    
                    try:
                        rel_path = os.path.relpath(image_path, static_dir)
                        web_path = '/static/' + rel_path.replace('\\', '/')
                        print(f'转换后Web路径: {web_path}')
                    except ValueError as e:
                        print(f'路径转换失败: {e}')
                        # 尝试其他方法
                        if 'static' in image_path:
                            # 从static开始截取
                            static_index = image_path.find('static')
                            if static_index != -1:
                                web_path = '/' + image_path[static_index:].replace('\\', '/')
                                print(f'备用Web路径: {web_path}')
            
            print('-' * 50)
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f'查询失败: {e}')

if __name__ == "__main__":
    check_image_paths()
